syntax = "proto3";

package monitor;
option go_package = "proto/monitor";

message Uint32Value { uint32 value = 1; }
message Uint64Value { uint64 value = 1; }

message EmptyResponse {}

message GetConditionRequest {
  string site = 1;
  uint32 group_id = 2;
  uint32 hall_id = 3;
  uint32 user_id = 4;
  repeated uint32 game_kinds = 5;
  uint32 monitor_id = 6;
}

message SubCondition {
  string condition_type = 1;
  uint64 win = 2;
  uint64 lose = 3;
  uint32 day = 4;
  uint32 percent = 5;
  uint64 bet_amount = 6;
}

message MonitorCondition {
  uint32 monitor_id = 1;
  string site = 2;
  uint32 group_id = 3;
  uint32 hall_id = 4;
  uint32 user_id = 5;
  repeated uint32 game_kind = 6;
  repeated SubCondition condition = 7;
}

message GetConditionResponse { repeated MonitorCondition monitors = 1; }

message CreateConditionRequest {
  CreateCondition condition = 1;
  CreateSubCondition sub_condition = 2;
  repeated uint32 game_kinds = 3;
}

message CreateCondition {
  string site = 1;
  uint32 group_id = 2;
  uint32 hall_id = 3;
  uint32 user_id = 4;
}

message CreateSubCondition {
  uint64 win = 1;
  uint64 lose = 2;
  uint64 member_win = 3;
  uint64 member_lose = 4;
  uint32 day = 5;
  uint32 percent = 6;
  uint64 bet_amount = 7;
}

message DeleteConditionRequest {
  uint32 monitor_id = 1;
  uint32 game_kind = 2;
}

message GetSubConditionRequest {
  string site = 1;
  uint32 group_id = 2;
  uint32 hall_id = 3;
  uint32 user_id = 4;
  uint32 monitor_id = 5;
  GetSubCondition sub_condition = 6;
}

message GetSubCondition {
  Uint64Value win = 1;
  Uint64Value lose = 2;
  Uint64Value member_win = 3;
  Uint64Value member_lose = 4;
  Uint32Value day = 5;
  Uint32Value percent = 6;
  Uint64Value bet_amount = 7;
}

message GetSubConditionResponse {
  uint32 monitor_id = 1;
  string site = 2;
  uint32 group_id = 3;
  uint32 hall_id = 4;
  uint32 user_id = 5;
  repeated uint32 game_kind = 6;
}

message UpdateConditionRequest {
  uint32 monitor_id = 1;
  repeated uint32 game_kinds = 2;
  repeated uint32 affected_monitor_id = 3;
}

message SendTelegramRequest {
  string service_name = 1;
  string msg = 2;
}

service Monitor {
  rpc CreateCondition(CreateConditionRequest) returns (EmptyResponse);
  rpc GetCondition(GetConditionRequest) returns (GetConditionResponse);
  rpc DeleteCondition(DeleteConditionRequest) returns (EmptyResponse);
  rpc GetSubCondition(GetSubConditionRequest) returns (GetSubConditionResponse);
  rpc UpdateCondition(UpdateConditionRequest) returns (EmptyResponse);
  rpc SendTelegram(SendTelegramRequest) returns (EmptyResponse);
}
