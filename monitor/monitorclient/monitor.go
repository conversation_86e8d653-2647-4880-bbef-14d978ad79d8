// Code generated by goctl. DO NOT EDIT.
// Source: monitor.proto

package monitorclient

import (
	"context"

	"gbh/proto/monitor"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CreateCondition         = monitor.CreateCondition
	CreateConditionRequest  = monitor.CreateConditionRequest
	CreateSubCondition      = monitor.CreateSubCondition
	DeleteConditionRequest  = monitor.DeleteConditionRequest
	EmptyResponse           = monitor.EmptyResponse
	GetConditionRequest     = monitor.GetConditionRequest
	GetConditionResponse    = monitor.GetConditionResponse
	GetSubCondition         = monitor.GetSubCondition
	GetSubConditionRequest  = monitor.GetSubConditionRequest
	GetSubConditionResponse = monitor.GetSubConditionResponse
	MonitorCondition        = monitor.MonitorCondition
	SendTelegramRequest     = monitor.SendTelegramRequest
	SubCondition            = monitor.SubCondition
	Uint32Value             = monitor.Uint32Value
	Uint64Value             = monitor.Uint64Value
	UpdateConditionRequest  = monitor.UpdateConditionRequest

	Monitor interface {
		CreateCondition(ctx context.Context, in *CreateConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetCondition(ctx context.Context, in *GetConditionRequest, opts ...grpc.CallOption) (*GetConditionResponse, error)
		DeleteCondition(ctx context.Context, in *DeleteConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetSubCondition(ctx context.Context, in *GetSubConditionRequest, opts ...grpc.CallOption) (*GetSubConditionResponse, error)
		UpdateCondition(ctx context.Context, in *UpdateConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SendTelegram(ctx context.Context, in *SendTelegramRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	}

	defaultMonitor struct {
		cli zrpc.Client
	}
)

func NewMonitor(cli zrpc.Client) Monitor {
	return &defaultMonitor{
		cli: cli,
	}
}

func (m *defaultMonitor) CreateCondition(ctx context.Context, in *CreateConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.CreateCondition(ctx, in, opts...)
}

func (m *defaultMonitor) GetCondition(ctx context.Context, in *GetConditionRequest, opts ...grpc.CallOption) (*GetConditionResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.GetCondition(ctx, in, opts...)
}

func (m *defaultMonitor) DeleteCondition(ctx context.Context, in *DeleteConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.DeleteCondition(ctx, in, opts...)
}

func (m *defaultMonitor) GetSubCondition(ctx context.Context, in *GetSubConditionRequest, opts ...grpc.CallOption) (*GetSubConditionResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.GetSubCondition(ctx, in, opts...)
}

func (m *defaultMonitor) UpdateCondition(ctx context.Context, in *UpdateConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.UpdateCondition(ctx, in, opts...)
}

func (m *defaultMonitor) SendTelegram(ctx context.Context, in *SendTelegramRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.SendTelegram(ctx, in, opts...)
}
