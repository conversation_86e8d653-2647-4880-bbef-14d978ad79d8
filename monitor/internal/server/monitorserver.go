// Code generated by goctl. DO NOT EDIT.
// Source: monitor.proto

package server

import (
	"context"

	"gbh/monitor/internal/logic"
	"gbh/monitor/internal/svc"
	"gbh/proto/monitor"
)

type MonitorServer struct {
	svcCtx *svc.ServiceContext
	monitor.UnimplementedMonitorServer
}

func NewMonitorServer(svcCtx *svc.ServiceContext) *MonitorServer {
	return &MonitorServer{
		svcCtx: svcCtx,
	}
}

func (s *MonitorServer) CreateCondition(ctx context.Context, in *monitor.CreateConditionRequest) (*monitor.EmptyResponse, error) {
	l := logic.NewCreateConditionLogic(ctx, s.svcCtx)
	return l.CreateCondition(in)
}

func (s *MonitorServer) GetCondition(ctx context.Context, in *monitor.GetConditionRequest) (*monitor.GetConditionResponse, error) {
	l := logic.NewGetConditionLogic(ctx, s.svcCtx)
	return l.GetCondition(in)
}

func (s *MonitorServer) DeleteCondition(ctx context.Context, in *monitor.DeleteConditionRequest) (*monitor.EmptyResponse, error) {
	l := logic.NewDeleteConditionLogic(ctx, s.svcCtx)
	return l.DeleteCondition(in)
}

func (s *MonitorServer) GetSubCondition(ctx context.Context, in *monitor.GetSubConditionRequest) (*monitor.GetSubConditionResponse, error) {
	l := logic.NewGetSubConditionLogic(ctx, s.svcCtx)
	return l.GetSubCondition(in)
}

func (s *MonitorServer) UpdateCondition(ctx context.Context, in *monitor.UpdateConditionRequest) (*monitor.EmptyResponse, error) {
	l := logic.NewUpdateConditionLogic(ctx, s.svcCtx)
	return l.UpdateCondition(in)
}

func (s *MonitorServer) SendTelegram(ctx context.Context, in *monitor.SendTelegramRequest) (*monitor.EmptyResponse, error) {
	l := logic.NewSendTelegramLogic(ctx, s.svcCtx)
	return l.SendTelegram(in)
}
