// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.7
// Source: maintain.proto

package server

import (
	"context"

	"gbh/maintain/internal/logic"
	"gbh/maintain/internal/svc"
	"gbh/proto/maintain"
)

type MaintainServer struct {
	svcCtx *svc.ServiceContext
	maintain.UnimplementedMaintainServer
}

func NewMaintainServer(svcCtx *svc.ServiceContext) *MaintainServer {
	return &MaintainServer{
		svcCtx: svcCtx,
	}
}

func (s *MaintainServer) GetMaintainByGameKind(ctx context.Context, in *maintain.GetMaintainByGameKindRequest) (*maintain.GetMaintainByGameKindResponse, error) {
	l := logic.NewGetMaintainByGameKindLogic(ctx, s.svcCtx)
	return l.GetMaintainByGameKind(in)
}

func (s *MaintainServer) Get(ctx context.Context, in *maintain.MaintainRequest) (*maintain.MaintainResponse, error) {
	l := logic.NewGetLogic(ctx, s.svcCtx)
	return l.Get(in)
}

func (s *MaintainServer) GetMaintainByGameKindFromRedis(ctx context.Context, in *maintain.GetMaintainByGameKindFromRedisRequest) (*maintain.GetMaintainByGameKindFromRedisResponse, error) {
	l := logic.NewGetMaintainByGameKindFromRedisLogic(ctx, s.svcCtx)
	return l.GetMaintainByGameKindFromRedis(in)
}

func (s *MaintainServer) GetMaintainByHallID(ctx context.Context, in *maintain.GetMaintainByHallIDRequest) (*maintain.GetMaintainByHallIDResponse, error) {
	l := logic.NewGetMaintainByHallIDLogic(ctx, s.svcCtx)
	return l.GetMaintainByHallID(in)
}

func (s *MaintainServer) FeatureEntranceMaintenance(ctx context.Context, in *maintain.FeatureEntranceMaintenanceRequest) (*maintain.FeatureEntranceMaintenanceResponse, error) {
	l := logic.NewFeatureEntranceMaintenanceLogic(ctx, s.svcCtx)
	return l.FeatureEntranceMaintenance(in)
}

func (s *MaintainServer) GetMaintainGameKind(ctx context.Context, in *maintain.EmptyRequest) (*maintain.GetMaintainGameKindResponse, error) {
	l := logic.NewGetMaintainGameKindLogic(ctx, s.svcCtx)
	return l.GetMaintainGameKind(in)
}

func (s *MaintainServer) CreateLogoutSchedule(ctx context.Context, in *maintain.CreateLogoutScheduleRequest) (*maintain.EmptyResponse, error) {
	l := logic.NewCreateLogoutScheduleLogic(ctx, s.svcCtx)
	return l.CreateLogoutSchedule(in)
}

func (s *MaintainServer) DeleteLogoutSchedule(ctx context.Context, in *maintain.DeleteLogoutScheduleRequest) (*maintain.EmptyResponse, error) {
	l := logic.NewDeleteLogoutScheduleLogic(ctx, s.svcCtx)
	return l.DeleteLogoutSchedule(in)
}

func (s *MaintainServer) UpdateDomainMaintenance(ctx context.Context, in *maintain.UpdateDomainMaintenanceRequest) (*maintain.EmptyResponse, error) {
	l := logic.NewUpdateDomainMaintenanceLogic(ctx, s.svcCtx)
	return l.UpdateDomainMaintenance(in)
}

func (s *MaintainServer) DeleteDomainMessage(ctx context.Context, in *maintain.DeleteDomainMessageRequest) (*maintain.EmptyResponse, error) {
	l := logic.NewDeleteDomainMessageLogic(ctx, s.svcCtx)
	return l.DeleteDomainMessage(in)
}
