// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.7
// Source: maintain.proto

package maintainclient

import (
	"context"

	"gbh/proto/maintain"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CreateLogoutScheduleRequest            = maintain.CreateLogoutScheduleRequest
	DeleteDomainMessageRequest             = maintain.DeleteDomainMessageRequest
	DeleteLogoutScheduleRequest            = maintain.DeleteLogoutScheduleRequest
	EmptyRequest                           = maintain.EmptyRequest
	EmptyResponse                          = maintain.EmptyResponse
	FeatureEntranceMaintenance             = maintain.FeatureEntranceMaintenance
	FeatureEntranceMaintenanceRequest      = maintain.FeatureEntranceMaintenanceRequest
	FeatureEntranceMaintenanceResponse     = maintain.FeatureEntranceMaintenanceResponse
	GetMaintainByGameKindFromRedisRequest  = maintain.GetMaintainByGameKindFromRedisRequest
	GetMaintainByGameKindFromRedisResponse = maintain.GetMaintainByGameKindFromRedisResponse
	GetMaintainByGameKindRequest           = maintain.GetMaintainByGameKindRequest
	GetMaintainByGameKindResponse          = maintain.GetMaintainByGameKindResponse
	GetMaintainByHallIDRequest             = maintain.GetMaintainByHallIDRequest
	GetMaintainByHallIDResponse            = maintain.GetMaintainByHallIDResponse
	GetMaintainGameKindResponse            = maintain.GetMaintainGameKindResponse
	MaintainRequest                        = maintain.MaintainRequest
	MaintainResponse                       = maintain.MaintainResponse
	StringValue                            = maintain.StringValue
	UpdateDomainMaintenanceRequest         = maintain.UpdateDomainMaintenanceRequest

	Maintain interface {
		GetMaintainByGameKind(ctx context.Context, in *GetMaintainByGameKindRequest, opts ...grpc.CallOption) (*GetMaintainByGameKindResponse, error)
		Get(ctx context.Context, in *MaintainRequest, opts ...grpc.CallOption) (*MaintainResponse, error)
		GetMaintainByGameKindFromRedis(ctx context.Context, in *GetMaintainByGameKindFromRedisRequest, opts ...grpc.CallOption) (*GetMaintainByGameKindFromRedisResponse, error)
		GetMaintainByHallID(ctx context.Context, in *GetMaintainByHallIDRequest, opts ...grpc.CallOption) (*GetMaintainByHallIDResponse, error)
		FeatureEntranceMaintenance(ctx context.Context, in *FeatureEntranceMaintenanceRequest, opts ...grpc.CallOption) (*FeatureEntranceMaintenanceResponse, error)
		GetMaintainGameKind(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetMaintainGameKindResponse, error)
		CreateLogoutSchedule(ctx context.Context, in *CreateLogoutScheduleRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		DeleteLogoutSchedule(ctx context.Context, in *DeleteLogoutScheduleRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		UpdateDomainMaintenance(ctx context.Context, in *UpdateDomainMaintenanceRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		DeleteDomainMessage(ctx context.Context, in *DeleteDomainMessageRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	}

	defaultMaintain struct {
		cli zrpc.Client
	}
)

func NewMaintain(cli zrpc.Client) Maintain {
	return &defaultMaintain{
		cli: cli,
	}
}

func (m *defaultMaintain) GetMaintainByGameKind(ctx context.Context, in *GetMaintainByGameKindRequest, opts ...grpc.CallOption) (*GetMaintainByGameKindResponse, error) {
	client := maintain.NewMaintainClient(m.cli.Conn())
	return client.GetMaintainByGameKind(ctx, in, opts...)
}

func (m *defaultMaintain) Get(ctx context.Context, in *MaintainRequest, opts ...grpc.CallOption) (*MaintainResponse, error) {
	client := maintain.NewMaintainClient(m.cli.Conn())
	return client.Get(ctx, in, opts...)
}

func (m *defaultMaintain) GetMaintainByGameKindFromRedis(ctx context.Context, in *GetMaintainByGameKindFromRedisRequest, opts ...grpc.CallOption) (*GetMaintainByGameKindFromRedisResponse, error) {
	client := maintain.NewMaintainClient(m.cli.Conn())
	return client.GetMaintainByGameKindFromRedis(ctx, in, opts...)
}

func (m *defaultMaintain) GetMaintainByHallID(ctx context.Context, in *GetMaintainByHallIDRequest, opts ...grpc.CallOption) (*GetMaintainByHallIDResponse, error) {
	client := maintain.NewMaintainClient(m.cli.Conn())
	return client.GetMaintainByHallID(ctx, in, opts...)
}

func (m *defaultMaintain) FeatureEntranceMaintenance(ctx context.Context, in *FeatureEntranceMaintenanceRequest, opts ...grpc.CallOption) (*FeatureEntranceMaintenanceResponse, error) {
	client := maintain.NewMaintainClient(m.cli.Conn())
	return client.FeatureEntranceMaintenance(ctx, in, opts...)
}

func (m *defaultMaintain) GetMaintainGameKind(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetMaintainGameKindResponse, error) {
	client := maintain.NewMaintainClient(m.cli.Conn())
	return client.GetMaintainGameKind(ctx, in, opts...)
}

func (m *defaultMaintain) CreateLogoutSchedule(ctx context.Context, in *CreateLogoutScheduleRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := maintain.NewMaintainClient(m.cli.Conn())
	return client.CreateLogoutSchedule(ctx, in, opts...)
}

func (m *defaultMaintain) DeleteLogoutSchedule(ctx context.Context, in *DeleteLogoutScheduleRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := maintain.NewMaintainClient(m.cli.Conn())
	return client.DeleteLogoutSchedule(ctx, in, opts...)
}

func (m *defaultMaintain) UpdateDomainMaintenance(ctx context.Context, in *UpdateDomainMaintenanceRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := maintain.NewMaintainClient(m.cli.Conn())
	return client.UpdateDomainMaintenance(ctx, in, opts...)
}

func (m *defaultMaintain) DeleteDomainMessage(ctx context.Context, in *DeleteDomainMessageRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := maintain.NewMaintainClient(m.cli.Conn())
	return client.DeleteDomainMessage(ctx, in, opts...)
}
