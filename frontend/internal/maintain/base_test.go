package maintain

import (
	"context"
	"database/sql"
	"gbh/maintain/maintainclient"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var (
	ctx        context.Context
	redisCache *redis.Client
	redisMock  redismock.ClientMock
	dbObj      *sql.DB
	sqlMock    sqlmock.Sqlmock
	gormDB     *gorm.DB
)

type mockMaintainRPC struct{ mock.Mock }

func (s *mockMaintainRPC) GetMaintainByGameKind(ctx context.Context, in *maintainclient.GetMaintainByGameKindRequest, _ ...grpc.CallOption) (*maintainclient.GetMaintainByGameKindResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.GetMaintainByGameKindResponse), nil
}

func (s *mockMaintainRPC) Get(ctx context.Context, in *maintainclient.MaintainRequest, _ ...grpc.CallOption) (*maintainclient.MaintainResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.MaintainResponse), nil
}

func (s *mockMaintainRPC) GetMaintainByGameKindFromRedis(ctx context.Context, in *maintainclient.GetMaintainByGameKindFromRedisRequest, _ ...grpc.CallOption) (*maintainclient.GetMaintainByGameKindFromRedisResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.GetMaintainByGameKindFromRedisResponse), nil
}

func (s *mockMaintainRPC) GetMaintainByHallID(ctx context.Context, in *maintainclient.GetMaintainByHallIDRequest, _ ...grpc.CallOption) (*maintainclient.GetMaintainByHallIDResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.GetMaintainByHallIDResponse), nil
}

func (s *mockMaintainRPC) FeatureEntranceMaintenance(ctx context.Context, in *maintainclient.FeatureEntranceMaintenanceRequest, _ ...grpc.CallOption) (*maintainclient.FeatureEntranceMaintenanceResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.FeatureEntranceMaintenanceResponse), nil
}

func (s *mockMaintainRPC) GetMaintainGameKind(ctx context.Context, in *maintainclient.EmptyRequest, _ ...grpc.CallOption) (*maintainclient.GetMaintainGameKindResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.GetMaintainGameKindResponse), nil
}

func (s *mockMaintainRPC) CreateLogoutSchedule(ctx context.Context, in *maintainclient.CreateLogoutScheduleRequest, _ ...grpc.CallOption) (*maintainclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.EmptyResponse), nil
}

func (s *mockMaintainRPC) DeleteLogoutSchedule(ctx context.Context, in *maintainclient.DeleteLogoutScheduleRequest, _ ...grpc.CallOption) (*maintainclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.EmptyResponse), nil
}

func (s *mockMaintainRPC) UpdateDomainMaintenance(ctx context.Context, in *maintainclient.UpdateDomainMaintenanceRequest, _ ...grpc.CallOption) (*maintainclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.EmptyResponse), nil
}

func (s *mockMaintainRPC) DeleteDomainMessage(ctx context.Context, in *maintainclient.DeleteDomainMessageRequest, _ ...grpc.CallOption) (*maintainclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.EmptyResponse), nil
}

func newMockMaintainRPC() *mockMaintainRPC {
	return &mockMaintainRPC{}
}

func init() {
	ctx = context.Background()

	redisCache, redisMock = redismock.NewClientMock()
	dbObj, sqlMock, _ = sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
	dialector := mysql.New(mysql.Config{
		DSN:                       "sqlmock_db_0",
		DriverName:                "mysql",
		Conn:                      dbObj,
		SkipInitializeWithVersion: true,
	})

	gormDB, _ = gorm.Open(dialector, &gorm.Config{})
}
