package types

type APIRoutePageRequest struct {
	Game   string `json:"game" form:"game,optional"`
	Token  string `json:"token" form:"token,optional"`
	Ots    string `json:"ots" form:"ots,optional"`
	Lang   string `json:"lang" form:"lang,optional"`
	Device string `json:"device" form:"device,optional"`
}

type GameRoutePageRequest struct {
	HallID    uint32 `json:"hall_id" form:"hall_id"`
	GameKind  uint32 `json:"game_kind" form:"game_kind"`
	GameID    uint32 `json:"game_id" form:"game_id"`
	Lang      string `json:"lang" form:"lang"`
	SessionID string `json:"session_id" form:"session_id"`
	IsMobile  bool   `json:"is_mobile" form:"is_mobile"`
}
