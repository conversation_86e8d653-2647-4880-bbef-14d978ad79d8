package logic

import (
	"errors"
	"gbh/errorx"
	"gbh/frontend/internal/common"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"
	"gbh/lotterygame/lotterygameclient"

	"github.com/dromara/carbon/v2"
	"github.com/gin-gonic/gin"
	"github.com/zeromicro/go-zero/core/logx"
)

type LotteryGameLobbyMenuLinkLogic struct {
	logx.Logger
	ctx    *gin.Context
	svcCtx *svc.ServiceContext
}

type LotteryGameLobbyMenuLinkResponse struct {
	GamePanel         *lotterygameclient.ShowcasePanel           `json:"game_panel,omitempty"`
	LikeGuess         []*lotterygameclient.RecommendPanel        `json:"like_guess,omitempty"`
	Official          []*lotterygameclient.CategoryOfficialPanel `json:"official,omitempty"`
	OfficialOn        string                                     `json:"official_on,omitempty"`
	Tradition         []*lotterygameclient.SingleTraditionPanel  `json:"tradition,omitempty"`
	TraditionEntrance string                                     `json:"tradition_entrance,omitempty"`
	ServerTimestamp   int64                                      `json:"server_timestamp"`
	Timezone          string                                     `json:"timezone"`
	LtCDN             string                                     `json:"lt_cdn"`
	LtBrowserIP       string                                     `json:"lt_browser_ip"`
}

func NewLotteryGameLobbyMenuLinkLogic(ctx *gin.Context, svcCtx *svc.ServiceContext) *LotteryGameLobbyMenuLinkLogic {
	return &LotteryGameLobbyMenuLinkLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LotteryGameLobbyMenuLinkLogic) LotteryGameLobbyMenuLink(req *types.LotteryGameLobbyMenuLink) (*types.BaseResponse, error) {
	// 玩家是否登入
	isLogin := false
	hallId := uint32(0)

	// 有登入
	if req.SessionID != "" {
		session, sessionErr := l.svcCtx.SessionCtx.Get(req.SessionID)
		if sessionErr != nil {
			return nil, sessionErr
		}

		user, userErr := l.svcCtx.UserCtx.GetUserByUserId(session.GetUser().GetId())
		if userErr != nil {
			return nil, userErr
		}

		// 會員停權
		if user.GetBankrupt() {
			return nil, errorx.BankruptError
		}

		// 遊戲是否開放
		if !isUserGameKindOpen(session.GetRdInfo().GetLobbySwitch(), constants.BBLottery) {
			return nil, errorx.GameIsNotOpen
		}

		isLogin = true
		hallId = user.GetDomain()
	}

	if req.SessionID == "" {
		hallId = req.HallID

		hallLobbySwitch, hallLobbySwitchErr := l.svcCtx.GameCtx.GetLobbySwitchByHallID(hallId, true, nil)
		if hallLobbySwitchErr != nil {
			return nil, hallLobbySwitchErr
		}

		// 遊戲是否開放
		if !isHallGameKindOpen(hallLobbySwitch) {
			return nil, errorx.GameIsNotOpen
		}
	}

	// 遊戲是否維護中
	maintain, maintainErr := l.svcCtx.MaintainCtx.GetMaintainByGameKindFromRedis(constants.BBLottery)
	if maintainErr != nil {
		if !errors.Is(maintainErr, errorx.MaintainNotFound) {
			return nil, maintainErr
		}
	}

	if maintain != nil {
		return &types.BaseResponse{
			Code:    errorx.GameIsUnderMaintenance.Code,
			Message: errorx.GameIsUnderMaintenance.Message,
			Data: types.MaintainList{
				MaintainInfo: []types.MaintainInfo{
					{
						GameKind:  constants.BBLottery,
						Message:   maintain.GetMessage(),
						StartTime: carbon.Parse(maintain.GetStartTime(), constants.TimezoneGMT4).ToRfc3339String(),
						EndTime:   carbon.Parse(maintain.GetEndTime(), constants.TimezoneGMT4).ToRfc3339String(),
					},
				},
			},
		}, nil
	}

	lang := common.ConvertLang(req.Lang)

	// CDN由SR提供，會從header傳進來
	ltCDN := l.ctx.GetHeader("HTTP_X_CDN_LT")
	if ltCDN != "" {
		ltCDN = "https://" + ltCDN
	}

	gameDomain, err := l.svcCtx.GameCtx.GameDomain(constants.BBLottery, hallId)
	if err != nil {
		return nil, err
	}

	ltBrowserIP := ""
	if len(gameDomain.GetDomain()) > 0 {
		ltBrowserIP = gameDomain.GetDomain()[0]
	}

	response := LotteryGameLobbyMenuLinkResponse{}

	if isLogin {
		// 從 cookie 取 mfid
		mfId := l.getCookieMfId()

		lobbyLogin, err := l.svcCtx.LotteryGameCtx.LobbyLogin(req.SessionID, lang, l.ctx.ClientIP(), req.Filter, mfId)
		if err != nil {
			return nil, err
		}

		// 登入的第一次進來，RD2會回傳 mfid, 並存入 cookie
		l.setCookieMfId(lobbyLogin.GetCookie().GetMfid())

		response = LotteryGameLobbyMenuLinkResponse{
			GamePanel:         lobbyLogin.GetShowcasePanel(),
			LikeGuess:         lobbyLogin.GetRecommendPanel(),
			Official:          lobbyLogin.GetCategoryOfficialPanel(),
			OfficialOn:        lobbyLogin.GetCategoryOfficialOn(),
			Tradition:         lobbyLogin.GetSingleTraditionPanel(),
			TraditionEntrance: lobbyLogin.GetNoleaderboardEntrence(),
		}
	} else {
		lobbyGuest, err := l.svcCtx.LotteryGameCtx.LobbyGuest(hallId, lang, l.ctx.ClientIP(), req.Filter)
		if err != nil {
			return nil, err
		}

		response = LotteryGameLobbyMenuLinkResponse{
			GamePanel:  lobbyGuest.GetShowcasePanel(),
			LikeGuess:  lobbyGuest.GetRecommendPanel(),
			Official:   lobbyGuest.GetCategoryOfficialPanel(),
			OfficialOn: lobbyGuest.GetCategoryOfficialOn(),
			Tradition:  lobbyGuest.GetSingleTraditionPanel(),
		}
	}

	now := carbon.Now(constants.TimezoneGMT4)
	response.ServerTimestamp = now.Timestamp()
	response.Timezone = now.Timezone()
	response.LtCDN = ltCDN
	response.LtBrowserIP = ltBrowserIP

	return &types.BaseResponse{
		Data: response,
	}, nil
}

func (l *LotteryGameLobbyMenuLinkLogic) getCookieMfId() string {
	cookieMfId, cookieErr := l.ctx.Cookie("lt_mf-id")
	if cookieErr != nil {
		return ""
	}

	return cookieMfId
}

func (l *LotteryGameLobbyMenuLinkLogic) setCookieMfId(mfId string) {
	if mfId != "" {
		l.ctx.SetCookie("lt_mf-id", mfId, 0, "/", l.ctx.Request.Host, false, false)
	}
}
