package logic

import (
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"
	"gbh/session/sessionclient"
	"net/http"
	"testing"

	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
)

func Test_LotteryGameLobbyMenuLinkLogic_WithSession(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBLottery).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameDomain", constants.BBLottery, seeder.User.GetDomain()).Return(&seeder.GameDomain, nil)

	lotteryGameCtx := mock.NewLotteryGameCtx()
	lotteryGameCtx.On("LobbyLogin", seeder.Session, constants.ZhTw, ginCtx.ClientIP(), "", "").Return(&seeder.LobbyLogin, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.LotteryGameCtx = lotteryGameCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	now := carbon.Now(constants.TimezoneGMT4)

	expectedResponse := &types.BaseResponse{
		Data: LotteryGameLobbyMenuLinkResponse{
			GamePanel:         seeder.LobbyLogin.GetShowcasePanel(),
			LikeGuess:         seeder.LobbyLogin.GetRecommendPanel(),
			Official:          seeder.LobbyLogin.GetCategoryOfficialPanel(),
			OfficialOn:        seeder.LobbyLogin.GetCategoryOfficialOn(),
			Tradition:         seeder.LobbyLogin.GetSingleTraditionPanel(),
			TraditionEntrance: seeder.LobbyLogin.GetNoleaderboardEntrence(),
			ServerTimestamp:   now.Timestamp(),
			Timezone:          now.Timezone(),
			LtCDN:             "",
			LtBrowserIP:       seeder.GameDomain.GetDomain()[0],
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LotteryGameLobbyMenuLinkLogic_WithSessionID_GetSessionError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_LotteryGameLobbyMenuLinkLogic_WithSessionID_GetUserByUserIdError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_LotteryGameLobbyMenuLinkLogic_WithSessionID_UserIsBankrupt(t *testing.T) {
	seeder.User.Bankrupt = true

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.BankruptError)
	assert.Nil(t, resp)

	seeder.User.Bankrupt = false
}

func Test_LotteryGameLobbyMenuLinkLogic_WithSessionID_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.GetSession.GetRdInfo().GetLobbySwitch()
	seeder.GetSession.RdInfo.LobbySwitch = []*sessionclient.LobbySwitch{
		{
			GameKind: constants.BBLottery,
			Switch:   false,
		},
	}

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.GetSession.RdInfo.LobbySwitch = originLobbySwitch
}

func Test_LotteryGameLobbyMenuLinkLogic_WithSessionID_GetMaintainByGameKindFromRedisError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBLottery).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_LotteryGameLobbyMenuLinkLogic_WithSessionID_GameIsUnderMaintenance(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBLottery).Return(&seeder.GetMaintainByGameKindFromRedis, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Code:    errorx.GameIsUnderMaintenance.Code,
		Message: errorx.GameIsUnderMaintenance.Message,
		Data: types.MaintainList{
			MaintainInfo: []types.MaintainInfo{
				{
					GameKind:  constants.BBLottery,
					Message:   seeder.GetMaintainByGameKindFromRedis.GetMessage(),
					StartTime: carbon.Parse(seeder.GetMaintainByGameKindFromRedis.GetStartTime(), constants.TimezoneGMT4).ToRfc3339String(),
					EndTime:   carbon.Parse(seeder.GetMaintainByGameKindFromRedis.GetEndTime(), constants.TimezoneGMT4).ToRfc3339String(),
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LotteryGameLobbyMenuLinkLogic_WithSession_GameDomainError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBLottery).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameDomain", constants.BBLottery, seeder.User.GetDomain()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_LotteryGameLobbyMenuLinkLogic_WithSession_LobbyLoginError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBLottery).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameDomain", constants.BBLottery, seeder.User.GetDomain()).Return(&seeder.GameDomain, nil)

	lotteryGameCtx := mock.NewLotteryGameCtx()
	lotteryGameCtx.On("LobbyLogin", seeder.Session, constants.ZhTw, ginCtx.ClientIP(), "", "").Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.LotteryGameCtx = lotteryGameCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_LotteryGameLobbyMenuLinkLogic_WithSession_getCookieMfId(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBLottery).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameDomain", constants.BBLottery, seeder.User.GetDomain()).Return(&seeder.GameDomain, nil)

	lotteryGameCtx := mock.NewLotteryGameCtx()
	lotteryGameCtx.On("LobbyLogin", seeder.Session, constants.ZhTw, ginCtx.ClientIP(), "", seeder.MfId).Return(&seeder.LobbyLogin, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.LotteryGameCtx = lotteryGameCtx

	ginCtx.Request.Header = make(http.Header)
	ginCtx.Request.AddCookie(&http.Cookie{
		Name:  "lt_mf-id",
		Value: seeder.MfId,
	})

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	now := carbon.Now(constants.TimezoneGMT4)

	expectedResponse := &types.BaseResponse{
		Data: LotteryGameLobbyMenuLinkResponse{
			GamePanel:         seeder.LobbyLogin.GetShowcasePanel(),
			LikeGuess:         seeder.LobbyLogin.GetRecommendPanel(),
			Official:          seeder.LobbyLogin.GetCategoryOfficialPanel(),
			OfficialOn:        seeder.LobbyLogin.GetCategoryOfficialOn(),
			Tradition:         seeder.LobbyLogin.GetSingleTraditionPanel(),
			TraditionEntrance: seeder.LobbyLogin.GetNoleaderboardEntrence(),
			ServerTimestamp:   now.Timestamp(),
			Timezone:          now.Timezone(),
			LtCDN:             "",
			LtBrowserIP:       seeder.GameDomain.GetDomain()[0],
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)

	ginCtx.Request.Header = nil
}

func Test_LotteryGameLobbyMenuLinkLogic_WithSession_SetHeader_LtCDN(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBLottery).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameDomain", constants.BBLottery, seeder.User.GetDomain()).Return(&seeder.GameDomain, nil)

	lotteryGameCtx := mock.NewLotteryGameCtx()
	lotteryGameCtx.On("LobbyLogin", seeder.Session, constants.ZhTw, ginCtx.ClientIP(), "", "").Return(&seeder.LobbyLogin, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.LotteryGameCtx = lotteryGameCtx

	ginCtx.Request.Header = make(http.Header)
	ginCtx.Request.Header.Set("HTTP_X_CDN_LT", "lottery.cdn.test")

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	now := carbon.Now(constants.TimezoneGMT4)

	expectedResponse := &types.BaseResponse{
		Data: LotteryGameLobbyMenuLinkResponse{
			GamePanel:         seeder.LobbyLogin.GetShowcasePanel(),
			LikeGuess:         seeder.LobbyLogin.GetRecommendPanel(),
			Official:          seeder.LobbyLogin.GetCategoryOfficialPanel(),
			OfficialOn:        seeder.LobbyLogin.GetCategoryOfficialOn(),
			Tradition:         seeder.LobbyLogin.GetSingleTraditionPanel(),
			TraditionEntrance: seeder.LobbyLogin.GetNoleaderboardEntrence(),
			ServerTimestamp:   now.Timestamp(),
			Timezone:          now.Timezone(),
			LtCDN:             "https://lottery.cdn.test",
			LtBrowserIP:       seeder.GameDomain.GetDomain()[0],
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)

	ginCtx.Request.Header = nil
}

func Test_LotteryGameLobbyMenuLinkLogic_WithHallID(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBLottery).Return(nil, errorx.MaintainNotFound)

	gameCtx.On("GameDomain", constants.BBLottery, uint32(seeder.BgpHallID)).Return(&seeder.GameDomain, nil)

	lotteryGameCtx := mock.NewLotteryGameCtx()
	lotteryGameCtx.On("LobbyGuest", uint32(seeder.BgpHallID), constants.ZhTw, ginCtx.ClientIP(), "").Return(&seeder.LobbyGuest, nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.LotteryGameCtx = lotteryGameCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	now := carbon.Now(constants.TimezoneGMT4)

	expectedResponse := &types.BaseResponse{
		Data: LotteryGameLobbyMenuLinkResponse{
			GamePanel:       seeder.LobbyGuest.GetShowcasePanel(),
			LikeGuess:       seeder.LobbyGuest.GetRecommendPanel(),
			Official:        seeder.LobbyGuest.GetCategoryOfficialPanel(),
			OfficialOn:      seeder.LobbyGuest.GetCategoryOfficialOn(),
			Tradition:       seeder.LobbyGuest.GetSingleTraditionPanel(),
			ServerTimestamp: now.Timestamp(),
			Timezone:        now.Timezone(),
			LtCDN:           "",
			LtBrowserIP:     seeder.GameDomain.GetDomain()[0],
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LotteryGameLobbyMenuLinkLogic_WithHallID_GetLobbySwitchByHallIDError(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_LotteryGameLobbyMenuLinkLogic_WithHallID_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.HallIDLobbySwitch.GetLobbySwitch()
	seeder.HallIDLobbySwitch.LobbySwitch = []*gameclient.LobbySwitch{
		{
			GameKind: constants.BBLottery,
			Switch:   false,
		},
	}

	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	svcCtx.GameCtx = gameCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.HallIDLobbySwitch.LobbySwitch = originLobbySwitch
}

func Test_LotteryGameLobbyMenuLinkLogic_WithHallID_LobbyGuestError(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBLottery).Return(nil, errorx.MaintainNotFound)

	gameCtx.On("GameDomain", constants.BBLottery, uint32(seeder.BgpHallID)).Return(&seeder.GameDomain, nil)

	lotteryGameCtx := mock.NewLotteryGameCtx()
	lotteryGameCtx.On("LobbyGuest", uint32(seeder.BgpHallID), constants.ZhTw, ginCtx.ClientIP(), "").Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.LotteryGameCtx = lotteryGameCtx

	l := NewLotteryGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.LotteryGameLobbyMenuLink(&types.LotteryGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}
