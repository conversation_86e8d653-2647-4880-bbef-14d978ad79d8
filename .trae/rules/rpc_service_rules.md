# GBH RPC 服務開發規則

## 專案概述
這是一個基於 go-zero 框架的微服務專案，使用 gRPC 進行服務間通信。專案採用 Protocol Buffers 定義服務接口，並遵循 Clean Architecture 架構模式。

## 技術棧
- **框架**: go-zero (gRPC)
- **語言**: Go 1.24+
- **協議**: Protocol Buffers 3
- **資料庫**: MySQL + GORM
- **快取**: Redis
- **日誌**: logrus

## 目錄結構規範

### RPC 服務標準目錄結構
```
{service_name}/
├── {service_name}.go          # 服務主入口
├── {service_name}.proto       # Protocol Buffers 定義
├── {service_name}client/      # 客戶端程式碼
│   └── {service_name}.go
├── etc/
│   └── {service_name}.yaml.example  # 配置範例
└── internal/
    ├── config/                # 配置結構
    ├── constants/             # 常數定義
    ├── logic/                 # 業務邏輯層
    ├── schema/                # 資料模型
    ├── server/                # gRPC 服務實現
    └── svc/                   # 服務上下文
```

## 程式碼規範

### 1. Protocol Buffers 定義
- 使用 proto3 語法
- 服務名稱使用 PascalCase
- 方法名稱使用 PascalCase
- 訊息類型使用 PascalCase
- 欄位名稱使用 snake_case
- 必須包含 `option go_package` 指令

### 2. 服務實現結構
```go
type {ServiceName}Server struct {
    svcCtx *svc.ServiceContext
    {package}.Unimplemented{ServiceName}Server
}

func New{ServiceName}Server(svcCtx *svc.ServiceContext) *{ServiceName}Server {
    return &{ServiceName}Server{
        svcCtx: svcCtx,
    }
}
```

### 3. Logic 層實現
```go
type {MethodName}Logic struct {
    ctx    context.Context
    svcCtx *svc.ServiceContext
    logx.Logger
}

func New{MethodName}Logic(ctx context.Context, svcCtx *svc.ServiceContext) *{MethodName}Logic {
    return &{MethodName}Logic{
        ctx:    ctx,
        svcCtx: svcCtx,
        Logger: logx.WithContext(ctx),
    }
}

func (l *{MethodName}Logic) {MethodName}(in *{package}.{RequestType}) (*{package}.{ResponseType}, error) {
    // 業務邏輯實現
    return &{package}.{ResponseType}{}, nil
}
```

### 4. 服務上下文 (svc)
```go
type ServiceContext struct {
    Config config.Config
    Logger logx.Logger
    // 外部依賴
}

func NewServiceContext(c config.Config, logger logx.Logger, extSvc ExternalContext) *ServiceContext {
    return &ServiceContext{
        Config: c,
        Logger: logger,
        // 初始化外部依賴
    }
}
```

## 開發最佳實踐

### 1. 錯誤處理
- 使用專案統一的 errorx 包處理錯誤
- 返回適當的 gRPC 狀態碼
- 記錄詳細的錯誤日誌

### 2. 日誌記錄
- 使用結構化日誌
- 記錄關鍵業務操作
- 包含 trace ID 用於鏈路追蹤

### 3. 資料驗證
- 在 Logic 層進行業務邏輯驗證
- 使用 Protocol Buffers 的內建驗證
- 對外部輸入進行嚴格驗證

### 4. 資料庫操作
- 使用 GORM 進行資料庫操作
- 實現適當的事務管理
- 使用連接池優化性能

### 5. 快取策略
- 使用 Redis 進行資料快取
- 實現快取失效策略
- 避免快取穿透和雪崩

## 測試規範

### 1. 單元測試
- 每個 Logic 方法都要有對應的測試
- 使用 testify 進行斷言
- 使用 mock 隔離外部依賴

### 2. 測試檔案命名
- 測試檔案以 `_test.go` 結尾
- 測試函數以 `Test` 開頭

## 部署配置

### 1. 配置檔案
- 使用 YAML 格式
- 區分不同環境配置
- 敏感資訊使用環境變數

### 2. 服務註冊
- 使用 go-zero 的服務發現機制
- 配置健康檢查端點

## 安全規範

### 1. 認證授權
- 實現 gRPC 攔截器進行認證
- 使用 JWT 或類似機制
- 實現細粒度的權限控制

### 2. 資料安全
- 敏感資料加密存儲
- 使用 TLS 加密傳輸
- 實現資料脫敏

## 性能優化

### 1. 連接管理
- 使用連接池
- 實現連接復用
- 配置適當的超時時間

### 2. 資源管理
- 及時釋放資源
- 使用 context 控制請求生命週期
- 實現熔斷和限流機制

## 監控和運維

### 1. 指標收集
- 收集業務指標
- 監控系統資源使用
- 實現分散式追蹤

### 2. 日誌管理
- 結構化日誌輸出
- 日誌等級管理
- 日誌輪轉和歸檔

## 程式碼生成

使用 go-zero 工具鏈：
```bash
# 生成 RPC 服務程式碼
goctl rpc protoc {service_name}.proto --go_out=. --go-grpc_out=. --zrpc_out=.

# 生成客戶端程式碼
goctl rpc template -o {service_name}client/{service_name}.go
```

## 版本管理

### 1. API 版本控制
- 使用語義化版本
- 向後相容性考慮
- 廢棄 API 的遷移策略

### 2. 依賴管理
- 使用 Go Modules
- 定期更新依賴
- 安全漏洞檢查