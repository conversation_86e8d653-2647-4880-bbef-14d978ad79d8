// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: maintain.proto

package maintain

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Maintain_GetMaintainByGameKind_FullMethodName          = "/maintain.Maintain/GetMaintainByGameKind"
	Maintain_Get_FullMethodName                            = "/maintain.Maintain/Get"
	Maintain_GetMaintainByGameKindFromRedis_FullMethodName = "/maintain.Maintain/GetMaintainByGameKindFromRedis"
	Maintain_GetMaintainByHallID_FullMethodName            = "/maintain.Maintain/GetMaintainByHallID"
	Maintain_FeatureEntranceMaintenance_FullMethodName     = "/maintain.Maintain/FeatureEntranceMaintenance"
	Maintain_GetMaintainGameKind_FullMethodName            = "/maintain.Maintain/GetMaintainGameKind"
	Maintain_CreateLogoutSchedule_FullMethodName           = "/maintain.Maintain/CreateLogoutSchedule"
	Maintain_DeleteLogoutSchedule_FullMethodName           = "/maintain.Maintain/DeleteLogoutSchedule"
	Maintain_UpdateDomainMaintenance_FullMethodName        = "/maintain.Maintain/UpdateDomainMaintenance"
	Maintain_DeleteDomainMessage_FullMethodName            = "/maintain.Maintain/DeleteDomainMessage"
)

// MaintainClient is the client API for Maintain service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MaintainClient interface {
	GetMaintainByGameKind(ctx context.Context, in *GetMaintainByGameKindRequest, opts ...grpc.CallOption) (*GetMaintainByGameKindResponse, error)
	Get(ctx context.Context, in *MaintainRequest, opts ...grpc.CallOption) (*MaintainResponse, error)
	GetMaintainByGameKindFromRedis(ctx context.Context, in *GetMaintainByGameKindFromRedisRequest, opts ...grpc.CallOption) (*GetMaintainByGameKindFromRedisResponse, error)
	GetMaintainByHallID(ctx context.Context, in *GetMaintainByHallIDRequest, opts ...grpc.CallOption) (*GetMaintainByHallIDResponse, error)
	FeatureEntranceMaintenance(ctx context.Context, in *FeatureEntranceMaintenanceRequest, opts ...grpc.CallOption) (*FeatureEntranceMaintenanceResponse, error)
	GetMaintainGameKind(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetMaintainGameKindResponse, error)
	CreateLogoutSchedule(ctx context.Context, in *CreateLogoutScheduleRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	DeleteLogoutSchedule(ctx context.Context, in *DeleteLogoutScheduleRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	UpdateDomainMaintenance(ctx context.Context, in *UpdateDomainMaintenanceRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	DeleteDomainMessage(ctx context.Context, in *DeleteDomainMessageRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
}

type maintainClient struct {
	cc grpc.ClientConnInterface
}

func NewMaintainClient(cc grpc.ClientConnInterface) MaintainClient {
	return &maintainClient{cc}
}

func (c *maintainClient) GetMaintainByGameKind(ctx context.Context, in *GetMaintainByGameKindRequest, opts ...grpc.CallOption) (*GetMaintainByGameKindResponse, error) {
	out := new(GetMaintainByGameKindResponse)
	err := c.cc.Invoke(ctx, Maintain_GetMaintainByGameKind_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maintainClient) Get(ctx context.Context, in *MaintainRequest, opts ...grpc.CallOption) (*MaintainResponse, error) {
	out := new(MaintainResponse)
	err := c.cc.Invoke(ctx, Maintain_Get_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maintainClient) GetMaintainByGameKindFromRedis(ctx context.Context, in *GetMaintainByGameKindFromRedisRequest, opts ...grpc.CallOption) (*GetMaintainByGameKindFromRedisResponse, error) {
	out := new(GetMaintainByGameKindFromRedisResponse)
	err := c.cc.Invoke(ctx, Maintain_GetMaintainByGameKindFromRedis_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maintainClient) GetMaintainByHallID(ctx context.Context, in *GetMaintainByHallIDRequest, opts ...grpc.CallOption) (*GetMaintainByHallIDResponse, error) {
	out := new(GetMaintainByHallIDResponse)
	err := c.cc.Invoke(ctx, Maintain_GetMaintainByHallID_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maintainClient) FeatureEntranceMaintenance(ctx context.Context, in *FeatureEntranceMaintenanceRequest, opts ...grpc.CallOption) (*FeatureEntranceMaintenanceResponse, error) {
	out := new(FeatureEntranceMaintenanceResponse)
	err := c.cc.Invoke(ctx, Maintain_FeatureEntranceMaintenance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maintainClient) GetMaintainGameKind(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetMaintainGameKindResponse, error) {
	out := new(GetMaintainGameKindResponse)
	err := c.cc.Invoke(ctx, Maintain_GetMaintainGameKind_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maintainClient) CreateLogoutSchedule(ctx context.Context, in *CreateLogoutScheduleRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Maintain_CreateLogoutSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maintainClient) DeleteLogoutSchedule(ctx context.Context, in *DeleteLogoutScheduleRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Maintain_DeleteLogoutSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maintainClient) UpdateDomainMaintenance(ctx context.Context, in *UpdateDomainMaintenanceRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Maintain_UpdateDomainMaintenance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maintainClient) DeleteDomainMessage(ctx context.Context, in *DeleteDomainMessageRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Maintain_DeleteDomainMessage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MaintainServer is the server API for Maintain service.
// All implementations must embed UnimplementedMaintainServer
// for forward compatibility
type MaintainServer interface {
	GetMaintainByGameKind(context.Context, *GetMaintainByGameKindRequest) (*GetMaintainByGameKindResponse, error)
	Get(context.Context, *MaintainRequest) (*MaintainResponse, error)
	GetMaintainByGameKindFromRedis(context.Context, *GetMaintainByGameKindFromRedisRequest) (*GetMaintainByGameKindFromRedisResponse, error)
	GetMaintainByHallID(context.Context, *GetMaintainByHallIDRequest) (*GetMaintainByHallIDResponse, error)
	FeatureEntranceMaintenance(context.Context, *FeatureEntranceMaintenanceRequest) (*FeatureEntranceMaintenanceResponse, error)
	GetMaintainGameKind(context.Context, *EmptyRequest) (*GetMaintainGameKindResponse, error)
	CreateLogoutSchedule(context.Context, *CreateLogoutScheduleRequest) (*EmptyResponse, error)
	DeleteLogoutSchedule(context.Context, *DeleteLogoutScheduleRequest) (*EmptyResponse, error)
	UpdateDomainMaintenance(context.Context, *UpdateDomainMaintenanceRequest) (*EmptyResponse, error)
	DeleteDomainMessage(context.Context, *DeleteDomainMessageRequest) (*EmptyResponse, error)
	mustEmbedUnimplementedMaintainServer()
}

// UnimplementedMaintainServer must be embedded to have forward compatible implementations.
type UnimplementedMaintainServer struct {
}

func (UnimplementedMaintainServer) GetMaintainByGameKind(context.Context, *GetMaintainByGameKindRequest) (*GetMaintainByGameKindResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMaintainByGameKind not implemented")
}
func (UnimplementedMaintainServer) Get(context.Context, *MaintainRequest) (*MaintainResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedMaintainServer) GetMaintainByGameKindFromRedis(context.Context, *GetMaintainByGameKindFromRedisRequest) (*GetMaintainByGameKindFromRedisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMaintainByGameKindFromRedis not implemented")
}
func (UnimplementedMaintainServer) GetMaintainByHallID(context.Context, *GetMaintainByHallIDRequest) (*GetMaintainByHallIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMaintainByHallID not implemented")
}
func (UnimplementedMaintainServer) FeatureEntranceMaintenance(context.Context, *FeatureEntranceMaintenanceRequest) (*FeatureEntranceMaintenanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FeatureEntranceMaintenance not implemented")
}
func (UnimplementedMaintainServer) GetMaintainGameKind(context.Context, *EmptyRequest) (*GetMaintainGameKindResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMaintainGameKind not implemented")
}
func (UnimplementedMaintainServer) CreateLogoutSchedule(context.Context, *CreateLogoutScheduleRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLogoutSchedule not implemented")
}
func (UnimplementedMaintainServer) DeleteLogoutSchedule(context.Context, *DeleteLogoutScheduleRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLogoutSchedule not implemented")
}
func (UnimplementedMaintainServer) UpdateDomainMaintenance(context.Context, *UpdateDomainMaintenanceRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDomainMaintenance not implemented")
}
func (UnimplementedMaintainServer) DeleteDomainMessage(context.Context, *DeleteDomainMessageRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDomainMessage not implemented")
}
func (UnimplementedMaintainServer) mustEmbedUnimplementedMaintainServer() {}

// UnsafeMaintainServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MaintainServer will
// result in compilation errors.
type UnsafeMaintainServer interface {
	mustEmbedUnimplementedMaintainServer()
}

func RegisterMaintainServer(s grpc.ServiceRegistrar, srv MaintainServer) {
	s.RegisterService(&Maintain_ServiceDesc, srv)
}

func _Maintain_GetMaintainByGameKind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMaintainByGameKindRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaintainServer).GetMaintainByGameKind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Maintain_GetMaintainByGameKind_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaintainServer).GetMaintainByGameKind(ctx, req.(*GetMaintainByGameKindRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Maintain_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MaintainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaintainServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Maintain_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaintainServer).Get(ctx, req.(*MaintainRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Maintain_GetMaintainByGameKindFromRedis_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMaintainByGameKindFromRedisRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaintainServer).GetMaintainByGameKindFromRedis(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Maintain_GetMaintainByGameKindFromRedis_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaintainServer).GetMaintainByGameKindFromRedis(ctx, req.(*GetMaintainByGameKindFromRedisRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Maintain_GetMaintainByHallID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMaintainByHallIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaintainServer).GetMaintainByHallID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Maintain_GetMaintainByHallID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaintainServer).GetMaintainByHallID(ctx, req.(*GetMaintainByHallIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Maintain_FeatureEntranceMaintenance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FeatureEntranceMaintenanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaintainServer).FeatureEntranceMaintenance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Maintain_FeatureEntranceMaintenance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaintainServer).FeatureEntranceMaintenance(ctx, req.(*FeatureEntranceMaintenanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Maintain_GetMaintainGameKind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaintainServer).GetMaintainGameKind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Maintain_GetMaintainGameKind_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaintainServer).GetMaintainGameKind(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Maintain_CreateLogoutSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLogoutScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaintainServer).CreateLogoutSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Maintain_CreateLogoutSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaintainServer).CreateLogoutSchedule(ctx, req.(*CreateLogoutScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Maintain_DeleteLogoutSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLogoutScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaintainServer).DeleteLogoutSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Maintain_DeleteLogoutSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaintainServer).DeleteLogoutSchedule(ctx, req.(*DeleteLogoutScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Maintain_UpdateDomainMaintenance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDomainMaintenanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaintainServer).UpdateDomainMaintenance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Maintain_UpdateDomainMaintenance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaintainServer).UpdateDomainMaintenance(ctx, req.(*UpdateDomainMaintenanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Maintain_DeleteDomainMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDomainMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaintainServer).DeleteDomainMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Maintain_DeleteDomainMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaintainServer).DeleteDomainMessage(ctx, req.(*DeleteDomainMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Maintain_ServiceDesc is the grpc.ServiceDesc for Maintain service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Maintain_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "maintain.Maintain",
	HandlerType: (*MaintainServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMaintainByGameKind",
			Handler:    _Maintain_GetMaintainByGameKind_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _Maintain_Get_Handler,
		},
		{
			MethodName: "GetMaintainByGameKindFromRedis",
			Handler:    _Maintain_GetMaintainByGameKindFromRedis_Handler,
		},
		{
			MethodName: "GetMaintainByHallID",
			Handler:    _Maintain_GetMaintainByHallID_Handler,
		},
		{
			MethodName: "FeatureEntranceMaintenance",
			Handler:    _Maintain_FeatureEntranceMaintenance_Handler,
		},
		{
			MethodName: "GetMaintainGameKind",
			Handler:    _Maintain_GetMaintainGameKind_Handler,
		},
		{
			MethodName: "CreateLogoutSchedule",
			Handler:    _Maintain_CreateLogoutSchedule_Handler,
		},
		{
			MethodName: "DeleteLogoutSchedule",
			Handler:    _Maintain_DeleteLogoutSchedule_Handler,
		},
		{
			MethodName: "UpdateDomainMaintenance",
			Handler:    _Maintain_UpdateDomainMaintenance_Handler,
		},
		{
			MethodName: "DeleteDomainMessage",
			Handler:    _Maintain_DeleteDomainMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "maintain.proto",
}
