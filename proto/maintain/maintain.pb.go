// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v3.19.4
// source: maintain.proto

package maintain

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StringValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *StringValue) Reset() {
	*x = StringValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringValue) ProtoMessage() {}

func (x *StringValue) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringValue.ProtoReflect.Descriptor instead.
func (*StringValue) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{0}
}

func (x *StringValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type GetMaintainByGameKindRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32       `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	ClientIp *StringValue `protobuf:"bytes,2,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
}

func (x *GetMaintainByGameKindRequest) Reset() {
	*x = GetMaintainByGameKindRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaintainByGameKindRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaintainByGameKindRequest) ProtoMessage() {}

func (x *GetMaintainByGameKindRequest) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaintainByGameKindRequest.ProtoReflect.Descriptor instead.
func (*GetMaintainByGameKindRequest) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{1}
}

func (x *GetMaintainByGameKindRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GetMaintainByGameKindRequest) GetClientIp() *StringValue {
	if x != nil {
		return x.ClientIp
	}
	return nil
}

type GetMaintainByGameKindResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsMaintaining bool   `protobuf:"varint,1,opt,name=is_maintaining,json=isMaintaining,proto3" json:"is_maintaining,omitempty"`
	InWhitelist   bool   `protobuf:"varint,2,opt,name=in_whitelist,json=inWhitelist,proto3" json:"in_whitelist,omitempty"`
	BeginAt       string `protobuf:"bytes,3,opt,name=begin_at,json=beginAt,proto3" json:"begin_at,omitempty"`
	EndAt         string `protobuf:"bytes,4,opt,name=end_at,json=endAt,proto3" json:"end_at,omitempty"`
	ModifiedAt    string `protobuf:"bytes,5,opt,name=modified_at,json=modifiedAt,proto3" json:"modified_at,omitempty"`
	Code          uint32 `protobuf:"varint,6,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string `protobuf:"bytes,7,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *GetMaintainByGameKindResponse) Reset() {
	*x = GetMaintainByGameKindResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaintainByGameKindResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaintainByGameKindResponse) ProtoMessage() {}

func (x *GetMaintainByGameKindResponse) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaintainByGameKindResponse.ProtoReflect.Descriptor instead.
func (*GetMaintainByGameKindResponse) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{2}
}

func (x *GetMaintainByGameKindResponse) GetIsMaintaining() bool {
	if x != nil {
		return x.IsMaintaining
	}
	return false
}

func (x *GetMaintainByGameKindResponse) GetInWhitelist() bool {
	if x != nil {
		return x.InWhitelist
	}
	return false
}

func (x *GetMaintainByGameKindResponse) GetBeginAt() string {
	if x != nil {
		return x.BeginAt
	}
	return ""
}

func (x *GetMaintainByGameKindResponse) GetEndAt() string {
	if x != nil {
		return x.EndAt
	}
	return ""
}

func (x *GetMaintainByGameKindResponse) GetModifiedAt() string {
	if x != nil {
		return x.ModifiedAt
	}
	return ""
}

func (x *GetMaintainByGameKindResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMaintainByGameKindResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type MaintainRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchType string `protobuf:"bytes,1,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"`
}

func (x *MaintainRequest) Reset() {
	*x = MaintainRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaintainRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintainRequest) ProtoMessage() {}

func (x *MaintainRequest) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintainRequest.ProtoReflect.Descriptor instead.
func (*MaintainRequest) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{3}
}

func (x *MaintainRequest) GetSearchType() string {
	if x != nil {
		return x.SearchType
	}
	return ""
}

type MaintainResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State bool `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *MaintainResponse) Reset() {
	*x = MaintainResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaintainResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintainResponse) ProtoMessage() {}

func (x *MaintainResponse) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintainResponse.ProtoReflect.Descriptor instead.
func (*MaintainResponse) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{4}
}

func (x *MaintainResponse) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

type GetMaintainByGameKindFromRedisRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32 `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *GetMaintainByGameKindFromRedisRequest) Reset() {
	*x = GetMaintainByGameKindFromRedisRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaintainByGameKindFromRedisRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaintainByGameKindFromRedisRequest) ProtoMessage() {}

func (x *GetMaintainByGameKindFromRedisRequest) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaintainByGameKindFromRedisRequest.ProtoReflect.Descriptor instead.
func (*GetMaintainByGameKindFromRedisRequest) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{5}
}

func (x *GetMaintainByGameKindFromRedisRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

type GetMaintainByGameKindFromRedisResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime string   `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   string   `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Message   string   `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	WhiteList []string `protobuf:"bytes,4,rep,name=white_list,json=whiteList,proto3" json:"white_list,omitempty"`
}

func (x *GetMaintainByGameKindFromRedisResponse) Reset() {
	*x = GetMaintainByGameKindFromRedisResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaintainByGameKindFromRedisResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaintainByGameKindFromRedisResponse) ProtoMessage() {}

func (x *GetMaintainByGameKindFromRedisResponse) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaintainByGameKindFromRedisResponse.ProtoReflect.Descriptor instead.
func (*GetMaintainByGameKindFromRedisResponse) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{6}
}

func (x *GetMaintainByGameKindFromRedisResponse) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetMaintainByGameKindFromRedisResponse) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetMaintainByGameKindFromRedisResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetMaintainByGameKindFromRedisResponse) GetWhiteList() []string {
	if x != nil {
		return x.WhiteList
	}
	return nil
}

type GetMaintainByHallIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId   uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	ClientIp string `protobuf:"bytes,2,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
}

func (x *GetMaintainByHallIDRequest) Reset() {
	*x = GetMaintainByHallIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaintainByHallIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaintainByHallIDRequest) ProtoMessage() {}

func (x *GetMaintainByHallIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaintainByHallIDRequest.ProtoReflect.Descriptor instead.
func (*GetMaintainByHallIDRequest) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{7}
}

func (x *GetMaintainByHallIDRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetMaintainByHallIDRequest) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

type GetMaintainByHallIDResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       bool   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	StartTime    string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime      string `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	OperatorTime string `protobuf:"bytes,4,opt,name=operator_time,json=operatorTime,proto3" json:"operator_time,omitempty"`
}

func (x *GetMaintainByHallIDResponse) Reset() {
	*x = GetMaintainByHallIDResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaintainByHallIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaintainByHallIDResponse) ProtoMessage() {}

func (x *GetMaintainByHallIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaintainByHallIDResponse.ProtoReflect.Descriptor instead.
func (*GetMaintainByHallIDResponse) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{8}
}

func (x *GetMaintainByHallIDResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *GetMaintainByHallIDResponse) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetMaintainByHallIDResponse) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetMaintainByHallIDResponse) GetOperatorTime() string {
	if x != nil {
		return x.OperatorTime
	}
	return ""
}

type FeatureEntranceMaintenanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionName []string `protobuf:"bytes,1,rep,name=permission_name,json=permissionName,proto3" json:"permission_name,omitempty"`
	Entrance       uint32   `protobuf:"varint,2,opt,name=entrance,proto3" json:"entrance,omitempty"`
}

func (x *FeatureEntranceMaintenanceRequest) Reset() {
	*x = FeatureEntranceMaintenanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureEntranceMaintenanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureEntranceMaintenanceRequest) ProtoMessage() {}

func (x *FeatureEntranceMaintenanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureEntranceMaintenanceRequest.ProtoReflect.Descriptor instead.
func (*FeatureEntranceMaintenanceRequest) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{9}
}

func (x *FeatureEntranceMaintenanceRequest) GetPermissionName() []string {
	if x != nil {
		return x.PermissionName
	}
	return nil
}

func (x *FeatureEntranceMaintenanceRequest) GetEntrance() uint32 {
	if x != nil {
		return x.Entrance
	}
	return 0
}

type FeatureEntranceMaintenanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaintainList []*FeatureEntranceMaintenance `protobuf:"bytes,1,rep,name=maintain_list,json=maintainList,proto3" json:"maintain_list,omitempty"`
}

func (x *FeatureEntranceMaintenanceResponse) Reset() {
	*x = FeatureEntranceMaintenanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureEntranceMaintenanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureEntranceMaintenanceResponse) ProtoMessage() {}

func (x *FeatureEntranceMaintenanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureEntranceMaintenanceResponse.ProtoReflect.Descriptor instead.
func (*FeatureEntranceMaintenanceResponse) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{10}
}

func (x *FeatureEntranceMaintenanceResponse) GetMaintainList() []*FeatureEntranceMaintenance {
	if x != nil {
		return x.MaintainList
	}
	return nil
}

type FeatureEntranceMaintenance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Feature        string `protobuf:"bytes,1,opt,name=feature,proto3" json:"feature,omitempty"`
	StartTime      string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime        string `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Memo           string `protobuf:"bytes,4,opt,name=memo,proto3" json:"memo,omitempty"`
	PermissionName string `protobuf:"bytes,5,opt,name=permission_name,json=permissionName,proto3" json:"permission_name,omitempty"`
	Entrance       uint32 `protobuf:"varint,6,opt,name=entrance,proto3" json:"entrance,omitempty"`
}

func (x *FeatureEntranceMaintenance) Reset() {
	*x = FeatureEntranceMaintenance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureEntranceMaintenance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureEntranceMaintenance) ProtoMessage() {}

func (x *FeatureEntranceMaintenance) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureEntranceMaintenance.ProtoReflect.Descriptor instead.
func (*FeatureEntranceMaintenance) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{11}
}

func (x *FeatureEntranceMaintenance) GetFeature() string {
	if x != nil {
		return x.Feature
	}
	return ""
}

func (x *FeatureEntranceMaintenance) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *FeatureEntranceMaintenance) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *FeatureEntranceMaintenance) GetMemo() string {
	if x != nil {
		return x.Memo
	}
	return ""
}

func (x *FeatureEntranceMaintenance) GetPermissionName() string {
	if x != nil {
		return x.PermissionName
	}
	return ""
}

func (x *FeatureEntranceMaintenance) GetEntrance() uint32 {
	if x != nil {
		return x.Entrance
	}
	return 0
}

type EmptyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{12}
}

type GetMaintainGameKindResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []uint32 `protobuf:"varint,1,rep,packed,name=data,proto3" json:"data,omitempty"`
}

func (x *GetMaintainGameKindResponse) Reset() {
	*x = GetMaintainGameKindResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaintainGameKindResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaintainGameKindResponse) ProtoMessage() {}

func (x *GetMaintainGameKindResponse) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaintainGameKindResponse.ProtoReflect.Descriptor instead.
func (*GetMaintainGameKindResponse) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{13}
}

func (x *GetMaintainGameKindResponse) GetData() []uint32 {
	if x != nil {
		return x.Data
	}
	return nil
}

type CreateLogoutScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	StartTime string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	UsedIn    string `protobuf:"bytes,3,opt,name=used_in,json=usedIn,proto3" json:"used_in,omitempty"`
}

func (x *CreateLogoutScheduleRequest) Reset() {
	*x = CreateLogoutScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLogoutScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLogoutScheduleRequest) ProtoMessage() {}

func (x *CreateLogoutScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLogoutScheduleRequest.ProtoReflect.Descriptor instead.
func (*CreateLogoutScheduleRequest) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{14}
}

func (x *CreateLogoutScheduleRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *CreateLogoutScheduleRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *CreateLogoutScheduleRequest) GetUsedIn() string {
	if x != nil {
		return x.UsedIn
	}
	return ""
}

type EmptyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{15}
}

type DeleteLogoutScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UsedIn string `protobuf:"bytes,2,opt,name=used_in,json=usedIn,proto3" json:"used_in,omitempty"`
}

func (x *DeleteLogoutScheduleRequest) Reset() {
	*x = DeleteLogoutScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLogoutScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLogoutScheduleRequest) ProtoMessage() {}

func (x *DeleteLogoutScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLogoutScheduleRequest.ProtoReflect.Descriptor instead.
func (*DeleteLogoutScheduleRequest) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteLogoutScheduleRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *DeleteLogoutScheduleRequest) GetUsedIn() string {
	if x != nil {
		return x.UsedIn
	}
	return ""
}

type UpdateDomainMaintenanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Website   string `protobuf:"bytes,2,opt,name=website,proto3" json:"website,omitempty"`
	StartTime string `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   string `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Operator  string `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
}

func (x *UpdateDomainMaintenanceRequest) Reset() {
	*x = UpdateDomainMaintenanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDomainMaintenanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDomainMaintenanceRequest) ProtoMessage() {}

func (x *UpdateDomainMaintenanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDomainMaintenanceRequest.ProtoReflect.Descriptor instead.
func (*UpdateDomainMaintenanceRequest) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateDomainMaintenanceRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *UpdateDomainMaintenanceRequest) GetWebsite() string {
	if x != nil {
		return x.Website
	}
	return ""
}

func (x *UpdateDomainMaintenanceRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *UpdateDomainMaintenanceRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *UpdateDomainMaintenanceRequest) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

type DeleteDomainMessageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId   uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UsedIn   string `protobuf:"bytes,2,opt,name=used_in,json=usedIn,proto3" json:"used_in,omitempty"`
	SendType uint32 `protobuf:"varint,3,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
}

func (x *DeleteDomainMessageRequest) Reset() {
	*x = DeleteDomainMessageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_maintain_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDomainMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDomainMessageRequest) ProtoMessage() {}

func (x *DeleteDomainMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_maintain_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDomainMessageRequest.ProtoReflect.Descriptor instead.
func (*DeleteDomainMessageRequest) Descriptor() ([]byte, []int) {
	return file_maintain_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteDomainMessageRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *DeleteDomainMessageRequest) GetUsedIn() string {
	if x != nil {
		return x.UsedIn
	}
	return ""
}

func (x *DeleteDomainMessageRequest) GetSendType() uint32 {
	if x != nil {
		return x.SendType
	}
	return 0
}

var File_maintain_proto protoreflect.FileDescriptor

var file_maintain_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x22, 0x23, 0x0a, 0x0b, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x6f, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x42, 0x79,
	0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x32, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70,
	0x22, 0xe2, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x42, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x4d, 0x61,
	0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x5f,
	0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x69, 0x6e, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x62, 0x65, 0x67, 0x69, 0x6e, 0x41, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x6e, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x41, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x32, 0x0a, 0x0f, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x22, 0x28, 0x0a, 0x10, 0x4d, 0x61, 0x69,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x22, 0x44, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x42, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x46, 0x72, 0x6f, 0x6d,
	0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x22, 0x9b, 0x01, 0x0a, 0x26, 0x47, 0x65,
	0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x42, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x4b,
	0x69, 0x6e, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x68, 0x69, 0x74,
	0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x52, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4d, 0x61,
	0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x42, 0x79, 0x48, 0x61, 0x6c, 0x6c, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x22, 0x94, 0x01, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x42, 0x79, 0x48, 0x61, 0x6c,
	0x6c, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0x68, 0x0a, 0x21, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x6f, 0x0a, 0x22,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x4d,
	0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x49, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x61, 0x69, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xc9, 0x01,
	0x0a, 0x1a, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6d, 0x65, 0x6d, 0x6f, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x0e, 0x0a, 0x0c, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x31, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x6e, 0x0a, 0x1b,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68,
	0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61,
	0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x64, 0x49, 0x6e, 0x22, 0x0f, 0x0a, 0x0d,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4f, 0x0a,
	0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68,
	0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x69, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x64, 0x49, 0x6e, 0x22, 0xa9,
	0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4d,
	0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x65,
	0x62, 0x73, 0x69, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x65, 0x62,
	0x73, 0x69, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x22, 0x6b, 0x0a, 0x1a, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x64, 0x49, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73,
	0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x32, 0xcf, 0x07, 0x0a, 0x08, 0x4d, 0x61, 0x69, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x12, 0x68, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x42, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x26, 0x2e,
	0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x42, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x42, 0x79, 0x47, 0x61,
	0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c,
	0x0a, 0x03, 0x47, 0x65, 0x74, 0x12, 0x19, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1a, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x42, 0x79, 0x47, 0x61,
	0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x64, 0x69, 0x73, 0x12,
	0x2f, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61,
	0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x42, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64,
	0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x30, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4d,
	0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x42, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e,
	0x64, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x62, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x42, 0x79, 0x48, 0x61, 0x6c, 0x6c, 0x49, 0x44, 0x12, 0x24, 0x2e, 0x6d, 0x61, 0x69, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x42, 0x79, 0x48, 0x61, 0x6c, 0x6c, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x25, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61,
	0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x42, 0x79, 0x48, 0x61, 0x6c, 0x6c, 0x49, 0x44, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x77, 0x0a, 0x1a, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x2b, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x4d,
	0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x61, 0x69, 0x6e,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x54, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x47, 0x61,
	0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x16, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25,
	0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c,
	0x6f, 0x67, 0x6f, 0x75, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x25, 0x2e,
	0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c,
	0x6f, 0x67, 0x6f, 0x75, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a,
	0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x25, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x6d,
	0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x28, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x6d, 0x61, 0x69,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x24, 0x2e, 0x6d, 0x61, 0x69,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x17, 0x2e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_maintain_proto_rawDescOnce sync.Once
	file_maintain_proto_rawDescData = file_maintain_proto_rawDesc
)

func file_maintain_proto_rawDescGZIP() []byte {
	file_maintain_proto_rawDescOnce.Do(func() {
		file_maintain_proto_rawDescData = protoimpl.X.CompressGZIP(file_maintain_proto_rawDescData)
	})
	return file_maintain_proto_rawDescData
}

var file_maintain_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_maintain_proto_goTypes = []any{
	(*StringValue)(nil),                            // 0: maintain.StringValue
	(*GetMaintainByGameKindRequest)(nil),           // 1: maintain.GetMaintainByGameKindRequest
	(*GetMaintainByGameKindResponse)(nil),          // 2: maintain.GetMaintainByGameKindResponse
	(*MaintainRequest)(nil),                        // 3: maintain.MaintainRequest
	(*MaintainResponse)(nil),                       // 4: maintain.MaintainResponse
	(*GetMaintainByGameKindFromRedisRequest)(nil),  // 5: maintain.GetMaintainByGameKindFromRedisRequest
	(*GetMaintainByGameKindFromRedisResponse)(nil), // 6: maintain.GetMaintainByGameKindFromRedisResponse
	(*GetMaintainByHallIDRequest)(nil),             // 7: maintain.GetMaintainByHallIDRequest
	(*GetMaintainByHallIDResponse)(nil),            // 8: maintain.GetMaintainByHallIDResponse
	(*FeatureEntranceMaintenanceRequest)(nil),      // 9: maintain.FeatureEntranceMaintenanceRequest
	(*FeatureEntranceMaintenanceResponse)(nil),     // 10: maintain.FeatureEntranceMaintenanceResponse
	(*FeatureEntranceMaintenance)(nil),             // 11: maintain.FeatureEntranceMaintenance
	(*EmptyRequest)(nil),                           // 12: maintain.EmptyRequest
	(*GetMaintainGameKindResponse)(nil),            // 13: maintain.GetMaintainGameKindResponse
	(*CreateLogoutScheduleRequest)(nil),            // 14: maintain.CreateLogoutScheduleRequest
	(*EmptyResponse)(nil),                          // 15: maintain.EmptyResponse
	(*DeleteLogoutScheduleRequest)(nil),            // 16: maintain.DeleteLogoutScheduleRequest
	(*UpdateDomainMaintenanceRequest)(nil),         // 17: maintain.UpdateDomainMaintenanceRequest
	(*DeleteDomainMessageRequest)(nil),             // 18: maintain.DeleteDomainMessageRequest
}
var file_maintain_proto_depIdxs = []int32{
	0,  // 0: maintain.GetMaintainByGameKindRequest.client_ip:type_name -> maintain.StringValue
	11, // 1: maintain.FeatureEntranceMaintenanceResponse.maintain_list:type_name -> maintain.FeatureEntranceMaintenance
	1,  // 2: maintain.Maintain.GetMaintainByGameKind:input_type -> maintain.GetMaintainByGameKindRequest
	3,  // 3: maintain.Maintain.Get:input_type -> maintain.MaintainRequest
	5,  // 4: maintain.Maintain.GetMaintainByGameKindFromRedis:input_type -> maintain.GetMaintainByGameKindFromRedisRequest
	7,  // 5: maintain.Maintain.GetMaintainByHallID:input_type -> maintain.GetMaintainByHallIDRequest
	9,  // 6: maintain.Maintain.FeatureEntranceMaintenance:input_type -> maintain.FeatureEntranceMaintenanceRequest
	12, // 7: maintain.Maintain.GetMaintainGameKind:input_type -> maintain.EmptyRequest
	14, // 8: maintain.Maintain.CreateLogoutSchedule:input_type -> maintain.CreateLogoutScheduleRequest
	16, // 9: maintain.Maintain.DeleteLogoutSchedule:input_type -> maintain.DeleteLogoutScheduleRequest
	17, // 10: maintain.Maintain.UpdateDomainMaintenance:input_type -> maintain.UpdateDomainMaintenanceRequest
	18, // 11: maintain.Maintain.DeleteDomainMessage:input_type -> maintain.DeleteDomainMessageRequest
	2,  // 12: maintain.Maintain.GetMaintainByGameKind:output_type -> maintain.GetMaintainByGameKindResponse
	4,  // 13: maintain.Maintain.Get:output_type -> maintain.MaintainResponse
	6,  // 14: maintain.Maintain.GetMaintainByGameKindFromRedis:output_type -> maintain.GetMaintainByGameKindFromRedisResponse
	8,  // 15: maintain.Maintain.GetMaintainByHallID:output_type -> maintain.GetMaintainByHallIDResponse
	10, // 16: maintain.Maintain.FeatureEntranceMaintenance:output_type -> maintain.FeatureEntranceMaintenanceResponse
	13, // 17: maintain.Maintain.GetMaintainGameKind:output_type -> maintain.GetMaintainGameKindResponse
	15, // 18: maintain.Maintain.CreateLogoutSchedule:output_type -> maintain.EmptyResponse
	15, // 19: maintain.Maintain.DeleteLogoutSchedule:output_type -> maintain.EmptyResponse
	15, // 20: maintain.Maintain.UpdateDomainMaintenance:output_type -> maintain.EmptyResponse
	15, // 21: maintain.Maintain.DeleteDomainMessage:output_type -> maintain.EmptyResponse
	12, // [12:22] is the sub-list for method output_type
	2,  // [2:12] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_maintain_proto_init() }
func file_maintain_proto_init() {
	if File_maintain_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_maintain_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*StringValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetMaintainByGameKindRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GetMaintainByGameKindResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*MaintainRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*MaintainResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*GetMaintainByGameKindFromRedisRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*GetMaintainByGameKindFromRedisResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*GetMaintainByHallIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*GetMaintainByHallIDResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*FeatureEntranceMaintenanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*FeatureEntranceMaintenanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*FeatureEntranceMaintenance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*EmptyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*GetMaintainGameKindResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*CreateLogoutScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*EmptyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteLogoutScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateDomainMaintenanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_maintain_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteDomainMessageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_maintain_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_maintain_proto_goTypes,
		DependencyIndexes: file_maintain_proto_depIdxs,
		MessageInfos:      file_maintain_proto_msgTypes,
	}.Build()
	File_maintain_proto = out.File
	file_maintain_proto_rawDesc = nil
	file_maintain_proto_goTypes = nil
	file_maintain_proto_depIdxs = nil
}
