// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.19.4
// source: monitor.proto

package monitor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Uint32Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Uint32Value) Reset() {
	*x = Uint32Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Uint32Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Uint32Value) ProtoMessage() {}

func (x *Uint32Value) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Uint32Value.ProtoReflect.Descriptor instead.
func (*Uint32Value) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{0}
}

func (x *Uint32Value) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type Uint64Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint64 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Uint64Value) Reset() {
	*x = Uint64Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Uint64Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Uint64Value) ProtoMessage() {}

func (x *Uint64Value) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Uint64Value.ProtoReflect.Descriptor instead.
func (*Uint64Value) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{1}
}

func (x *Uint64Value) GetValue() uint64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type EmptyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{2}
}

type GetConditionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Site      string   `protobuf:"bytes,1,opt,name=site,proto3" json:"site,omitempty"`
	GroupId   uint32   `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId    uint32   `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId    uint32   `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameKinds []uint32 `protobuf:"varint,5,rep,packed,name=game_kinds,json=gameKinds,proto3" json:"game_kinds,omitempty"`
	MonitorId uint32   `protobuf:"varint,6,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
}

func (x *GetConditionRequest) Reset() {
	*x = GetConditionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConditionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConditionRequest) ProtoMessage() {}

func (x *GetConditionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConditionRequest.ProtoReflect.Descriptor instead.
func (*GetConditionRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{3}
}

func (x *GetConditionRequest) GetSite() string {
	if x != nil {
		return x.Site
	}
	return ""
}

func (x *GetConditionRequest) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GetConditionRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetConditionRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetConditionRequest) GetGameKinds() []uint32 {
	if x != nil {
		return x.GameKinds
	}
	return nil
}

func (x *GetConditionRequest) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

type SubCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConditionType string `protobuf:"bytes,1,opt,name=condition_type,json=conditionType,proto3" json:"condition_type,omitempty"`
	Win           uint64 `protobuf:"varint,2,opt,name=win,proto3" json:"win,omitempty"`
	Lose          uint64 `protobuf:"varint,3,opt,name=lose,proto3" json:"lose,omitempty"`
	Day           uint32 `protobuf:"varint,4,opt,name=day,proto3" json:"day,omitempty"`
	Percent       uint32 `protobuf:"varint,5,opt,name=percent,proto3" json:"percent,omitempty"`
	BetAmount     uint64 `protobuf:"varint,6,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
}

func (x *SubCondition) Reset() {
	*x = SubCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubCondition) ProtoMessage() {}

func (x *SubCondition) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubCondition.ProtoReflect.Descriptor instead.
func (*SubCondition) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{4}
}

func (x *SubCondition) GetConditionType() string {
	if x != nil {
		return x.ConditionType
	}
	return ""
}

func (x *SubCondition) GetWin() uint64 {
	if x != nil {
		return x.Win
	}
	return 0
}

func (x *SubCondition) GetLose() uint64 {
	if x != nil {
		return x.Lose
	}
	return 0
}

func (x *SubCondition) GetDay() uint32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *SubCondition) GetPercent() uint32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

func (x *SubCondition) GetBetAmount() uint64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

type MonitorCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MonitorId uint32          `protobuf:"varint,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	Site      string          `protobuf:"bytes,2,opt,name=site,proto3" json:"site,omitempty"`
	GroupId   uint32          `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId    uint32          `protobuf:"varint,4,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId    uint32          `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameKind  []uint32        `protobuf:"varint,6,rep,packed,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	Condition []*SubCondition `protobuf:"bytes,7,rep,name=condition,proto3" json:"condition,omitempty"`
}

func (x *MonitorCondition) Reset() {
	*x = MonitorCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorCondition) ProtoMessage() {}

func (x *MonitorCondition) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorCondition.ProtoReflect.Descriptor instead.
func (*MonitorCondition) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{5}
}

func (x *MonitorCondition) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

func (x *MonitorCondition) GetSite() string {
	if x != nil {
		return x.Site
	}
	return ""
}

func (x *MonitorCondition) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *MonitorCondition) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *MonitorCondition) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *MonitorCondition) GetGameKind() []uint32 {
	if x != nil {
		return x.GameKind
	}
	return nil
}

func (x *MonitorCondition) GetCondition() []*SubCondition {
	if x != nil {
		return x.Condition
	}
	return nil
}

type GetConditionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Monitors []*MonitorCondition `protobuf:"bytes,1,rep,name=monitors,proto3" json:"monitors,omitempty"`
}

func (x *GetConditionResponse) Reset() {
	*x = GetConditionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConditionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConditionResponse) ProtoMessage() {}

func (x *GetConditionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConditionResponse.ProtoReflect.Descriptor instead.
func (*GetConditionResponse) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{6}
}

func (x *GetConditionResponse) GetMonitors() []*MonitorCondition {
	if x != nil {
		return x.Monitors
	}
	return nil
}

type CreateConditionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Condition    *CreateCondition    `protobuf:"bytes,1,opt,name=condition,proto3" json:"condition,omitempty"`
	SubCondition *CreateSubCondition `protobuf:"bytes,2,opt,name=sub_condition,json=subCondition,proto3" json:"sub_condition,omitempty"`
	GameKinds    []uint32            `protobuf:"varint,3,rep,packed,name=game_kinds,json=gameKinds,proto3" json:"game_kinds,omitempty"`
}

func (x *CreateConditionRequest) Reset() {
	*x = CreateConditionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateConditionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateConditionRequest) ProtoMessage() {}

func (x *CreateConditionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateConditionRequest.ProtoReflect.Descriptor instead.
func (*CreateConditionRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{7}
}

func (x *CreateConditionRequest) GetCondition() *CreateCondition {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *CreateConditionRequest) GetSubCondition() *CreateSubCondition {
	if x != nil {
		return x.SubCondition
	}
	return nil
}

func (x *CreateConditionRequest) GetGameKinds() []uint32 {
	if x != nil {
		return x.GameKinds
	}
	return nil
}

type CreateCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Site    string `protobuf:"bytes,1,opt,name=site,proto3" json:"site,omitempty"`
	GroupId uint32 `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId  uint32 `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId  uint32 `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *CreateCondition) Reset() {
	*x = CreateCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCondition) ProtoMessage() {}

func (x *CreateCondition) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCondition.ProtoReflect.Descriptor instead.
func (*CreateCondition) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{8}
}

func (x *CreateCondition) GetSite() string {
	if x != nil {
		return x.Site
	}
	return ""
}

func (x *CreateCondition) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *CreateCondition) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *CreateCondition) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CreateSubCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Win        uint64 `protobuf:"varint,1,opt,name=win,proto3" json:"win,omitempty"`
	Lose       uint64 `protobuf:"varint,2,opt,name=lose,proto3" json:"lose,omitempty"`
	MemberWin  uint64 `protobuf:"varint,3,opt,name=member_win,json=memberWin,proto3" json:"member_win,omitempty"`
	MemberLose uint64 `protobuf:"varint,4,opt,name=member_lose,json=memberLose,proto3" json:"member_lose,omitempty"`
	Day        uint32 `protobuf:"varint,5,opt,name=day,proto3" json:"day,omitempty"`
	Percent    uint32 `protobuf:"varint,6,opt,name=percent,proto3" json:"percent,omitempty"`
	BetAmount  uint64 `protobuf:"varint,7,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
}

func (x *CreateSubCondition) Reset() {
	*x = CreateSubCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSubCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSubCondition) ProtoMessage() {}

func (x *CreateSubCondition) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSubCondition.ProtoReflect.Descriptor instead.
func (*CreateSubCondition) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{9}
}

func (x *CreateSubCondition) GetWin() uint64 {
	if x != nil {
		return x.Win
	}
	return 0
}

func (x *CreateSubCondition) GetLose() uint64 {
	if x != nil {
		return x.Lose
	}
	return 0
}

func (x *CreateSubCondition) GetMemberWin() uint64 {
	if x != nil {
		return x.MemberWin
	}
	return 0
}

func (x *CreateSubCondition) GetMemberLose() uint64 {
	if x != nil {
		return x.MemberLose
	}
	return 0
}

func (x *CreateSubCondition) GetDay() uint32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *CreateSubCondition) GetPercent() uint32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

func (x *CreateSubCondition) GetBetAmount() uint64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

type DeleteConditionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MonitorId uint32 `protobuf:"varint,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	GameKind  uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *DeleteConditionRequest) Reset() {
	*x = DeleteConditionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteConditionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConditionRequest) ProtoMessage() {}

func (x *DeleteConditionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConditionRequest.ProtoReflect.Descriptor instead.
func (*DeleteConditionRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteConditionRequest) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

func (x *DeleteConditionRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

type GetSubConditionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Site         string           `protobuf:"bytes,1,opt,name=site,proto3" json:"site,omitempty"`
	GroupId      uint32           `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId       uint32           `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId       uint32           `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	MonitorId    uint32           `protobuf:"varint,5,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	SubCondition *GetSubCondition `protobuf:"bytes,6,opt,name=sub_condition,json=subCondition,proto3" json:"sub_condition,omitempty"`
}

func (x *GetSubConditionRequest) Reset() {
	*x = GetSubConditionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubConditionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubConditionRequest) ProtoMessage() {}

func (x *GetSubConditionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubConditionRequest.ProtoReflect.Descriptor instead.
func (*GetSubConditionRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{11}
}

func (x *GetSubConditionRequest) GetSite() string {
	if x != nil {
		return x.Site
	}
	return ""
}

func (x *GetSubConditionRequest) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GetSubConditionRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetSubConditionRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetSubConditionRequest) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

func (x *GetSubConditionRequest) GetSubCondition() *GetSubCondition {
	if x != nil {
		return x.SubCondition
	}
	return nil
}

type GetSubCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Win        *Uint64Value `protobuf:"bytes,1,opt,name=win,proto3" json:"win,omitempty"`
	Lose       *Uint64Value `protobuf:"bytes,2,opt,name=lose,proto3" json:"lose,omitempty"`
	MemberWin  *Uint64Value `protobuf:"bytes,3,opt,name=member_win,json=memberWin,proto3" json:"member_win,omitempty"`
	MemberLose *Uint64Value `protobuf:"bytes,4,opt,name=member_lose,json=memberLose,proto3" json:"member_lose,omitempty"`
	Day        *Uint32Value `protobuf:"bytes,5,opt,name=day,proto3" json:"day,omitempty"`
	Percent    *Uint32Value `protobuf:"bytes,6,opt,name=percent,proto3" json:"percent,omitempty"`
	BetAmount  *Uint64Value `protobuf:"bytes,7,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
}

func (x *GetSubCondition) Reset() {
	*x = GetSubCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubCondition) ProtoMessage() {}

func (x *GetSubCondition) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubCondition.ProtoReflect.Descriptor instead.
func (*GetSubCondition) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{12}
}

func (x *GetSubCondition) GetWin() *Uint64Value {
	if x != nil {
		return x.Win
	}
	return nil
}

func (x *GetSubCondition) GetLose() *Uint64Value {
	if x != nil {
		return x.Lose
	}
	return nil
}

func (x *GetSubCondition) GetMemberWin() *Uint64Value {
	if x != nil {
		return x.MemberWin
	}
	return nil
}

func (x *GetSubCondition) GetMemberLose() *Uint64Value {
	if x != nil {
		return x.MemberLose
	}
	return nil
}

func (x *GetSubCondition) GetDay() *Uint32Value {
	if x != nil {
		return x.Day
	}
	return nil
}

func (x *GetSubCondition) GetPercent() *Uint32Value {
	if x != nil {
		return x.Percent
	}
	return nil
}

func (x *GetSubCondition) GetBetAmount() *Uint64Value {
	if x != nil {
		return x.BetAmount
	}
	return nil
}

type GetSubConditionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MonitorId uint32   `protobuf:"varint,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	Site      string   `protobuf:"bytes,2,opt,name=site,proto3" json:"site,omitempty"`
	GroupId   uint32   `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId    uint32   `protobuf:"varint,4,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId    uint32   `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameKind  []uint32 `protobuf:"varint,6,rep,packed,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *GetSubConditionResponse) Reset() {
	*x = GetSubConditionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubConditionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubConditionResponse) ProtoMessage() {}

func (x *GetSubConditionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubConditionResponse.ProtoReflect.Descriptor instead.
func (*GetSubConditionResponse) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{13}
}

func (x *GetSubConditionResponse) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

func (x *GetSubConditionResponse) GetSite() string {
	if x != nil {
		return x.Site
	}
	return ""
}

func (x *GetSubConditionResponse) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GetSubConditionResponse) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetSubConditionResponse) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetSubConditionResponse) GetGameKind() []uint32 {
	if x != nil {
		return x.GameKind
	}
	return nil
}

type UpdateConditionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MonitorId         uint32   `protobuf:"varint,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	GameKinds         []uint32 `protobuf:"varint,2,rep,packed,name=game_kinds,json=gameKinds,proto3" json:"game_kinds,omitempty"`
	AffectedMonitorId []uint32 `protobuf:"varint,3,rep,packed,name=affected_monitor_id,json=affectedMonitorId,proto3" json:"affected_monitor_id,omitempty"`
}

func (x *UpdateConditionRequest) Reset() {
	*x = UpdateConditionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateConditionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateConditionRequest) ProtoMessage() {}

func (x *UpdateConditionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateConditionRequest.ProtoReflect.Descriptor instead.
func (*UpdateConditionRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateConditionRequest) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

func (x *UpdateConditionRequest) GetGameKinds() []uint32 {
	if x != nil {
		return x.GameKinds
	}
	return nil
}

func (x *UpdateConditionRequest) GetAffectedMonitorId() []uint32 {
	if x != nil {
		return x.AffectedMonitorId
	}
	return nil
}

type SendTelegramRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceName string `protobuf:"bytes,1,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	Msg         string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SendTelegramRequest) Reset() {
	*x = SendTelegramRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendTelegramRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTelegramRequest) ProtoMessage() {}

func (x *SendTelegramRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTelegramRequest.ProtoReflect.Descriptor instead.
func (*SendTelegramRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{15}
}

func (x *SendTelegramRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *SendTelegramRequest) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

var File_monitor_proto protoreflect.FileDescriptor

var file_monitor_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x07, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x22, 0x23, 0x0a, 0x0b, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x23, 0x0a,
	0x0b, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x0f, 0x0a, 0x0d, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0xb4, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61,
	0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c,
	0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0d,
	0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xa6, 0x01, 0x0a, 0x0c, 0x53,
	0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x77, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x03, 0x77, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x04, 0x6c, 0x6f, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x64, 0x61, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0xe4, 0x01, 0x0a, 0x10, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d,
	0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x33, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4d, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x08, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x73, 0x22, 0xb1, 0x01, 0x0a, 0x16, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0d,
	0x73, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0c, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0d, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x73, 0x22, 0x72, 0x0a,
	0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x73, 0x69, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0xc5, 0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x77, 0x69, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x77, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f,
	0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x6c, 0x6f, 0x73, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x77, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x57, 0x69, 0x6e, 0x12, 0x1f, 0x0a,
	0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x6f, 0x73, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x64, 0x61, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65,
	0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x54, 0x0a, 0x16, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x22,
	0xd7, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0d, 0x73, 0x75,
	0x62, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x73, 0x75, 0x62,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xdc, 0x02, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a,
	0x03, 0x77, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x03, 0x77, 0x69, 0x6e, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x6f, 0x73, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x6c, 0x6f, 0x73, 0x65, 0x12,
	0x33, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x77, 0x69, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x57, 0x69, 0x6e, 0x12, 0x35, 0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6c,
	0x6f, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x6f, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x03, 0x64,
	0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x03,
	0x64, 0x61, 0x79, 0x12, 0x2e, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x70, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x62,
	0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb6, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e,
	0x64, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e,
	0x64, 0x22, 0x86, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52,
	0x09, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x11, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x4a, 0x0a, 0x13, 0x53, 0x65,
	0x6e, 0x64, 0x54, 0x65, 0x6c, 0x65, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x32, 0xd6, 0x03, 0x0a, 0x07, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x12, 0x4a, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b,
	0x0a, 0x0c, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0f, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x75,
	0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a,
	0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1f, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0c, 0x53, 0x65, 0x6e,
	0x64, 0x54, 0x65, 0x6c, 0x65, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x1c, 0x2e, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x6c, 0x65, 0x67, 0x72, 0x61, 0x6d,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x0f, 0x5a, 0x0d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_monitor_proto_rawDescOnce sync.Once
	file_monitor_proto_rawDescData = file_monitor_proto_rawDesc
)

func file_monitor_proto_rawDescGZIP() []byte {
	file_monitor_proto_rawDescOnce.Do(func() {
		file_monitor_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_proto_rawDescData)
	})
	return file_monitor_proto_rawDescData
}

var file_monitor_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_monitor_proto_goTypes = []interface{}{
	(*Uint32Value)(nil),             // 0: monitor.Uint32Value
	(*Uint64Value)(nil),             // 1: monitor.Uint64Value
	(*EmptyResponse)(nil),           // 2: monitor.EmptyResponse
	(*GetConditionRequest)(nil),     // 3: monitor.GetConditionRequest
	(*SubCondition)(nil),            // 4: monitor.SubCondition
	(*MonitorCondition)(nil),        // 5: monitor.MonitorCondition
	(*GetConditionResponse)(nil),    // 6: monitor.GetConditionResponse
	(*CreateConditionRequest)(nil),  // 7: monitor.CreateConditionRequest
	(*CreateCondition)(nil),         // 8: monitor.CreateCondition
	(*CreateSubCondition)(nil),      // 9: monitor.CreateSubCondition
	(*DeleteConditionRequest)(nil),  // 10: monitor.DeleteConditionRequest
	(*GetSubConditionRequest)(nil),  // 11: monitor.GetSubConditionRequest
	(*GetSubCondition)(nil),         // 12: monitor.GetSubCondition
	(*GetSubConditionResponse)(nil), // 13: monitor.GetSubConditionResponse
	(*UpdateConditionRequest)(nil),  // 14: monitor.UpdateConditionRequest
	(*SendTelegramRequest)(nil),     // 15: monitor.SendTelegramRequest
}
var file_monitor_proto_depIdxs = []int32{
	4,  // 0: monitor.MonitorCondition.condition:type_name -> monitor.SubCondition
	5,  // 1: monitor.GetConditionResponse.monitors:type_name -> monitor.MonitorCondition
	8,  // 2: monitor.CreateConditionRequest.condition:type_name -> monitor.CreateCondition
	9,  // 3: monitor.CreateConditionRequest.sub_condition:type_name -> monitor.CreateSubCondition
	12, // 4: monitor.GetSubConditionRequest.sub_condition:type_name -> monitor.GetSubCondition
	1,  // 5: monitor.GetSubCondition.win:type_name -> monitor.Uint64Value
	1,  // 6: monitor.GetSubCondition.lose:type_name -> monitor.Uint64Value
	1,  // 7: monitor.GetSubCondition.member_win:type_name -> monitor.Uint64Value
	1,  // 8: monitor.GetSubCondition.member_lose:type_name -> monitor.Uint64Value
	0,  // 9: monitor.GetSubCondition.day:type_name -> monitor.Uint32Value
	0,  // 10: monitor.GetSubCondition.percent:type_name -> monitor.Uint32Value
	1,  // 11: monitor.GetSubCondition.bet_amount:type_name -> monitor.Uint64Value
	7,  // 12: monitor.Monitor.CreateCondition:input_type -> monitor.CreateConditionRequest
	3,  // 13: monitor.Monitor.GetCondition:input_type -> monitor.GetConditionRequest
	10, // 14: monitor.Monitor.DeleteCondition:input_type -> monitor.DeleteConditionRequest
	11, // 15: monitor.Monitor.GetSubCondition:input_type -> monitor.GetSubConditionRequest
	14, // 16: monitor.Monitor.UpdateCondition:input_type -> monitor.UpdateConditionRequest
	15, // 17: monitor.Monitor.SendTelegram:input_type -> monitor.SendTelegramRequest
	2,  // 18: monitor.Monitor.CreateCondition:output_type -> monitor.EmptyResponse
	6,  // 19: monitor.Monitor.GetCondition:output_type -> monitor.GetConditionResponse
	2,  // 20: monitor.Monitor.DeleteCondition:output_type -> monitor.EmptyResponse
	13, // 21: monitor.Monitor.GetSubCondition:output_type -> monitor.GetSubConditionResponse
	2,  // 22: monitor.Monitor.UpdateCondition:output_type -> monitor.EmptyResponse
	2,  // 23: monitor.Monitor.SendTelegram:output_type -> monitor.EmptyResponse
	18, // [18:24] is the sub-list for method output_type
	12, // [12:18] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_monitor_proto_init() }
func file_monitor_proto_init() {
	if File_monitor_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_monitor_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Uint32Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Uint64Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConditionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConditionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateConditionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSubCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteConditionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubConditionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubConditionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateConditionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendTelegramRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_monitor_proto_goTypes,
		DependencyIndexes: file_monitor_proto_depIdxs,
		MessageInfos:      file_monitor_proto_msgTypes,
	}.Build()
	File_monitor_proto = out.File
	file_monitor_proto_rawDesc = nil
	file_monitor_proto_goTypes = nil
	file_monitor_proto_depIdxs = nil
}
