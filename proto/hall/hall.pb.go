// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v3.19.4
// source: hall.proto

package hall

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BoolValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value bool `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *BoolValue) Reset() {
	*x = BoolValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoolValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoolValue) ProtoMessage() {}

func (x *BoolValue) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoolValue.ProtoReflect.Descriptor instead.
func (*BoolValue) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{0}
}

func (x *BoolValue) GetValue() bool {
	if x != nil {
		return x.Value
	}
	return false
}

type EmptyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{1}
}

type EmptyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{2}
}

type GetHallIdByWebsiteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Website string `protobuf:"bytes,1,opt,name=website,proto3" json:"website,omitempty"`
}

func (x *GetHallIdByWebsiteRequest) Reset() {
	*x = GetHallIdByWebsiteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHallIdByWebsiteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHallIdByWebsiteRequest) ProtoMessage() {}

func (x *GetHallIdByWebsiteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHallIdByWebsiteRequest.ProtoReflect.Descriptor instead.
func (*GetHallIdByWebsiteRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{3}
}

func (x *GetHallIdByWebsiteRequest) GetWebsite() string {
	if x != nil {
		return x.Website
	}
	return ""
}

type GetHallIdByWebsiteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *GetHallIdByWebsiteResponse) Reset() {
	*x = GetHallIdByWebsiteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHallIdByWebsiteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHallIdByWebsiteResponse) ProtoMessage() {}

func (x *GetHallIdByWebsiteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHallIdByWebsiteResponse.ProtoReflect.Descriptor instead.
func (*GetHallIdByWebsiteResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{4}
}

func (x *GetHallIdByWebsiteResponse) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type GetHallByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *GetHallByIdRequest) Reset() {
	*x = GetHallByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHallByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHallByIdRequest) ProtoMessage() {}

func (x *GetHallByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHallByIdRequest.ProtoReflect.Descriptor instead.
func (*GetHallByIdRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{5}
}

func (x *GetHallByIdRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type GetHallByIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	LoginCode string `protobuf:"bytes,3,opt,name=login_code,json=loginCode,proto3" json:"login_code,omitempty"`
	Enable    bool   `protobuf:"varint,4,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *GetHallByIdResponse) Reset() {
	*x = GetHallByIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHallByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHallByIdResponse) ProtoMessage() {}

func (x *GetHallByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHallByIdResponse.ProtoReflect.Descriptor instead.
func (*GetHallByIdResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{6}
}

func (x *GetHallByIdResponse) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetHallByIdResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetHallByIdResponse) GetLoginCode() string {
	if x != nil {
		return x.LoginCode
	}
	return ""
}

func (x *GetHallByIdResponse) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type HallListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable *BoolValue `protobuf:"bytes,1,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *HallListRequest) Reset() {
	*x = HallListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HallListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HallListRequest) ProtoMessage() {}

func (x *HallListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HallListRequest.ProtoReflect.Descriptor instead.
func (*HallListRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{7}
}

func (x *HallListRequest) GetEnable() *BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

type HallListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*GetHallByIdResponse `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *HallListResponse) Reset() {
	*x = HallListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HallListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HallListResponse) ProtoMessage() {}

func (x *HallListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HallListResponse.ProtoReflect.Descriptor instead.
func (*HallListResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{8}
}

func (x *HallListResponse) GetData() []*GetHallByIdResponse {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetHallSiteListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SiteList []*SiteList `protobuf:"bytes,1,rep,name=site_list,json=siteList,proto3" json:"site_list,omitempty"`
}

func (x *GetHallSiteListResponse) Reset() {
	*x = GetHallSiteListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHallSiteListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHallSiteListResponse) ProtoMessage() {}

func (x *GetHallSiteListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHallSiteListResponse.ProtoReflect.Descriptor instead.
func (*GetHallSiteListResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{9}
}

func (x *GetHallSiteListResponse) GetSiteList() []*SiteList {
	if x != nil {
		return x.SiteList
	}
	return nil
}

type SiteList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BigGroup  string `protobuf:"bytes,1,opt,name=big_group,json=bigGroup,proto3" json:"big_group,omitempty"`
	SiteGroup string `protobuf:"bytes,2,opt,name=site_group,json=siteGroup,proto3" json:"site_group,omitempty"`
	SiteName  string `protobuf:"bytes,3,opt,name=site_name,json=siteName,proto3" json:"site_name,omitempty"`
	LoginCode string `protobuf:"bytes,4,opt,name=login_code,json=loginCode,proto3" json:"login_code,omitempty"`
	HallId    uint32 `protobuf:"varint,5,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *SiteList) Reset() {
	*x = SiteList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SiteList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SiteList) ProtoMessage() {}

func (x *SiteList) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SiteList.ProtoReflect.Descriptor instead.
func (*SiteList) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{10}
}

func (x *SiteList) GetBigGroup() string {
	if x != nil {
		return x.BigGroup
	}
	return ""
}

func (x *SiteList) GetSiteGroup() string {
	if x != nil {
		return x.SiteGroup
	}
	return ""
}

func (x *SiteList) GetSiteName() string {
	if x != nil {
		return x.SiteName
	}
	return ""
}

func (x *SiteList) GetLoginCode() string {
	if x != nil {
		return x.LoginCode
	}
	return ""
}

func (x *SiteList) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type GetCurrencyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *GetCurrencyRequest) Reset() {
	*x = GetCurrencyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrencyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrencyRequest) ProtoMessage() {}

func (x *GetCurrencyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrencyRequest.ProtoReflect.Descriptor instead.
func (*GetCurrencyRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{11}
}

func (x *GetCurrencyRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type GetCurrencyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrencyInfo []*CurrencyInfo `protobuf:"bytes,1,rep,name=currency_info,json=currencyInfo,proto3" json:"currency_info,omitempty"`
}

func (x *GetCurrencyResponse) Reset() {
	*x = GetCurrencyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrencyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrencyResponse) ProtoMessage() {}

func (x *GetCurrencyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrencyResponse.ProtoReflect.Descriptor instead.
func (*GetCurrencyResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{12}
}

func (x *GetCurrencyResponse) GetCurrencyInfo() []*CurrencyInfo {
	if x != nil {
		return x.CurrencyInfo
	}
	return nil
}

type CurrencyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Preset   bool   `protobuf:"varint,1,opt,name=preset,proto3" json:"preset,omitempty"`
	Currency string `protobuf:"bytes,2,opt,name=currency,proto3" json:"currency,omitempty"`
}

func (x *CurrencyInfo) Reset() {
	*x = CurrencyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrencyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrencyInfo) ProtoMessage() {}

func (x *CurrencyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrencyInfo.ProtoReflect.Descriptor instead.
func (*CurrencyInfo) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{13}
}

func (x *CurrencyInfo) GetPreset() bool {
	if x != nil {
		return x.Preset
	}
	return false
}

func (x *CurrencyInfo) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

type GetPopUpBulletinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *GetPopUpBulletinRequest) Reset() {
	*x = GetPopUpBulletinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPopUpBulletinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPopUpBulletinRequest) ProtoMessage() {}

func (x *GetPopUpBulletinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPopUpBulletinRequest.ProtoReflect.Descriptor instead.
func (*GetPopUpBulletinRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{14}
}

func (x *GetPopUpBulletinRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type GetPopUpBulletinResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Enable    bool   `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	Role      uint32 `protobuf:"varint,3,opt,name=role,proto3" json:"role,omitempty"`
	ZhTw      string `protobuf:"bytes,4,opt,name=zh_tw,json=zhTw,proto3" json:"zh_tw,omitempty"`
	ZhCn      string `protobuf:"bytes,5,opt,name=zh_cn,json=zhCn,proto3" json:"zh_cn,omitempty"`
	En        string `protobuf:"bytes,6,opt,name=en,proto3" json:"en,omitempty"`
	Th        string `protobuf:"bytes,7,opt,name=th,proto3" json:"th,omitempty"`
	Ja        string `protobuf:"bytes,8,opt,name=ja,proto3" json:"ja,omitempty"`
	Ko        string `protobuf:"bytes,9,opt,name=ko,proto3" json:"ko,omitempty"`
	Vi        string `protobuf:"bytes,10,opt,name=vi,proto3" json:"vi,omitempty"`
	CreatedAt string `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *GetPopUpBulletinResponse) Reset() {
	*x = GetPopUpBulletinResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPopUpBulletinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPopUpBulletinResponse) ProtoMessage() {}

func (x *GetPopUpBulletinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPopUpBulletinResponse.ProtoReflect.Descriptor instead.
func (*GetPopUpBulletinResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{15}
}

func (x *GetPopUpBulletinResponse) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetPopUpBulletinResponse) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *GetPopUpBulletinResponse) GetRole() uint32 {
	if x != nil {
		return x.Role
	}
	return 0
}

func (x *GetPopUpBulletinResponse) GetZhTw() string {
	if x != nil {
		return x.ZhTw
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetZhCn() string {
	if x != nil {
		return x.ZhCn
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetEn() string {
	if x != nil {
		return x.En
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetTh() string {
	if x != nil {
		return x.Th
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetJa() string {
	if x != nil {
		return x.Ja
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetKo() string {
	if x != nil {
		return x.Ko
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetVi() string {
	if x != nil {
		return x.Vi
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type UpdatePopUpBulletinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32     `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Enable *BoolValue `protobuf:"bytes,2,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *UpdatePopUpBulletinRequest) Reset() {
	*x = UpdatePopUpBulletinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePopUpBulletinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePopUpBulletinRequest) ProtoMessage() {}

func (x *UpdatePopUpBulletinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePopUpBulletinRequest.ProtoReflect.Descriptor instead.
func (*UpdatePopUpBulletinRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{16}
}

func (x *UpdatePopUpBulletinRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *UpdatePopUpBulletinRequest) GetEnable() *BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

type DeletePopUpBulletinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *DeletePopUpBulletinRequest) Reset() {
	*x = DeletePopUpBulletinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePopUpBulletinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePopUpBulletinRequest) ProtoMessage() {}

func (x *DeletePopUpBulletinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePopUpBulletinRequest.ProtoReflect.Descriptor instead.
func (*DeletePopUpBulletinRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{17}
}

func (x *DeletePopUpBulletinRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type SetHallConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	LoginCode string `protobuf:"bytes,2,opt,name=login_code,json=loginCode,proto3" json:"login_code,omitempty"`
	Name      string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetHallConfigRequest) Reset() {
	*x = SetHallConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetHallConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetHallConfigRequest) ProtoMessage() {}

func (x *SetHallConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetHallConfigRequest.ProtoReflect.Descriptor instead.
func (*SetHallConfigRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{18}
}

func (x *SetHallConfigRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *SetHallConfigRequest) GetLoginCode() string {
	if x != nil {
		return x.LoginCode
	}
	return ""
}

func (x *SetHallConfigRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type SetCurrencyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId       uint32   `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	CurrencyList []string `protobuf:"bytes,2,rep,name=currency_list,json=currencyList,proto3" json:"currency_list,omitempty"`
}

func (x *SetCurrencyRequest) Reset() {
	*x = SetCurrencyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCurrencyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCurrencyRequest) ProtoMessage() {}

func (x *SetCurrencyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCurrencyRequest.ProtoReflect.Descriptor instead.
func (*SetCurrencyRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{19}
}

func (x *SetCurrencyRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *SetCurrencyRequest) GetCurrencyList() []string {
	if x != nil {
		return x.CurrencyList
	}
	return nil
}

type SetPresetCurrencyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId   uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Currency string `protobuf:"bytes,2,opt,name=currency,proto3" json:"currency,omitempty"`
}

func (x *SetPresetCurrencyRequest) Reset() {
	*x = SetPresetCurrencyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPresetCurrencyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPresetCurrencyRequest) ProtoMessage() {}

func (x *SetPresetCurrencyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPresetCurrencyRequest.ProtoReflect.Descriptor instead.
func (*SetPresetCurrencyRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{20}
}

func (x *SetPresetCurrencyRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *SetPresetCurrencyRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

var File_hall_proto protoreflect.FileDescriptor

var file_hall_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x68, 0x61,
	0x6c, 0x6c, 0x22, 0x21, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x0e, 0x0a, 0x0c, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x0f, 0x0a, 0x0d, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x35, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c,
	0x6c, 0x49, 0x64, 0x42, 0x79, 0x57, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x22, 0x35, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x42, 0x79, 0x57, 0x65, 0x62, 0x73,
	0x69, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68,
	0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61,
	0x6c, 0x6c, 0x49, 0x64, 0x22, 0x2d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x42,
	0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61,
	0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c,
	0x6c, 0x49, 0x64, 0x22, 0x79, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x42, 0x79,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61,
	0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c,
	0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x3a,
	0x0a, 0x0f, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x27, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x41, 0x0a, 0x10, 0x48, 0x61,
	0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68,
	0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x42, 0x79, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x46, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x53, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x09, 0x73, 0x69, 0x74, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x68, 0x61,
	0x6c, 0x6c, 0x2e, 0x53, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x08, 0x73, 0x69, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x9b, 0x01, 0x0a, 0x08, 0x53, 0x69, 0x74, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x67, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x67, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1b,
	0x0a, 0x09, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x73, 0x69, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c,
	0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61,
	0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c,
	0x6c, 0x49, 0x64, 0x22, 0x2d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c,
	0x49, 0x64, 0x22, 0x4e, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x0d, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x42, 0x0a, 0x0c, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x22, 0x32, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x70,
	0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x22, 0xf8, 0x01, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x13, 0x0a, 0x05,
	0x7a, 0x68, 0x5f, 0x74, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x68, 0x54,
	0x77, 0x12, 0x13, 0x0a, 0x05, 0x7a, 0x68, 0x5f, 0x63, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x7a, 0x68, 0x43, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x65, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x74, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x6a, 0x61, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x6a, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x6b, 0x6f, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x6b, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x76, 0x69, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x76, 0x69, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x5e, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x68,
	0x61, 0x6c, 0x6c, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x35, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x22, 0x62, 0x0a, 0x14,
	0x53, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x52, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x4f, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65,
	0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x32, 0xa4, 0x06, 0x0a, 0x04, 0x48, 0x61, 0x6c, 0x6c, 0x12, 0x57,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x42, 0x79, 0x57, 0x65, 0x62,
	0x73, 0x69, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x48,
	0x61, 0x6c, 0x6c, 0x49, 0x64, 0x42, 0x79, 0x57, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74,
	0x48, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x42, 0x79, 0x57, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x48, 0x61,
	0x6c, 0x6c, 0x42, 0x79, 0x49, 0x64, 0x12, 0x18, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65,
	0x74, 0x48, 0x61, 0x6c, 0x6c, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x19, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x42,
	0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x47,
	0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15, 0x2e, 0x68, 0x61, 0x6c,
	0x6c, 0x2e, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x48, 0x61, 0x6c, 0x6c, 0x53, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x2e, 0x68,
	0x61, 0x6c, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1d, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x53,
	0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x42, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x18,
	0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42,
	0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x12, 0x1d, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x12, 0x20, 0x2e,
	0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x70, 0x55, 0x70,
	0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x13, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f,
	0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x12, 0x20, 0x2e, 0x68, 0x61,
	0x6c, 0x6c, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42, 0x75,
	0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e,
	0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x1a, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x53, 0x65, 0x74, 0x48, 0x61,
	0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x13, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x53, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x18, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e,
	0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x48, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1e, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x53,
	0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x0c, 0x5a, 0x0a,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x6c, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_hall_proto_rawDescOnce sync.Once
	file_hall_proto_rawDescData = file_hall_proto_rawDesc
)

func file_hall_proto_rawDescGZIP() []byte {
	file_hall_proto_rawDescOnce.Do(func() {
		file_hall_proto_rawDescData = protoimpl.X.CompressGZIP(file_hall_proto_rawDescData)
	})
	return file_hall_proto_rawDescData
}

var file_hall_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_hall_proto_goTypes = []any{
	(*BoolValue)(nil),                  // 0: hall.BoolValue
	(*EmptyRequest)(nil),               // 1: hall.EmptyRequest
	(*EmptyResponse)(nil),              // 2: hall.EmptyResponse
	(*GetHallIdByWebsiteRequest)(nil),  // 3: hall.GetHallIdByWebsiteRequest
	(*GetHallIdByWebsiteResponse)(nil), // 4: hall.GetHallIdByWebsiteResponse
	(*GetHallByIdRequest)(nil),         // 5: hall.GetHallByIdRequest
	(*GetHallByIdResponse)(nil),        // 6: hall.GetHallByIdResponse
	(*HallListRequest)(nil),            // 7: hall.HallListRequest
	(*HallListResponse)(nil),           // 8: hall.HallListResponse
	(*GetHallSiteListResponse)(nil),    // 9: hall.GetHallSiteListResponse
	(*SiteList)(nil),                   // 10: hall.SiteList
	(*GetCurrencyRequest)(nil),         // 11: hall.GetCurrencyRequest
	(*GetCurrencyResponse)(nil),        // 12: hall.GetCurrencyResponse
	(*CurrencyInfo)(nil),               // 13: hall.CurrencyInfo
	(*GetPopUpBulletinRequest)(nil),    // 14: hall.GetPopUpBulletinRequest
	(*GetPopUpBulletinResponse)(nil),   // 15: hall.GetPopUpBulletinResponse
	(*UpdatePopUpBulletinRequest)(nil), // 16: hall.UpdatePopUpBulletinRequest
	(*DeletePopUpBulletinRequest)(nil), // 17: hall.DeletePopUpBulletinRequest
	(*SetHallConfigRequest)(nil),       // 18: hall.SetHallConfigRequest
	(*SetCurrencyRequest)(nil),         // 19: hall.SetCurrencyRequest
	(*SetPresetCurrencyRequest)(nil),   // 20: hall.SetPresetCurrencyRequest
}
var file_hall_proto_depIdxs = []int32{
	0,  // 0: hall.HallListRequest.enable:type_name -> hall.BoolValue
	6,  // 1: hall.HallListResponse.data:type_name -> hall.GetHallByIdResponse
	10, // 2: hall.GetHallSiteListResponse.site_list:type_name -> hall.SiteList
	13, // 3: hall.GetCurrencyResponse.currency_info:type_name -> hall.CurrencyInfo
	0,  // 4: hall.UpdatePopUpBulletinRequest.enable:type_name -> hall.BoolValue
	3,  // 5: hall.Hall.GetHallIdByWebsite:input_type -> hall.GetHallIdByWebsiteRequest
	5,  // 6: hall.Hall.GetHallById:input_type -> hall.GetHallByIdRequest
	7,  // 7: hall.Hall.GetHallList:input_type -> hall.HallListRequest
	1,  // 8: hall.Hall.GetHallSiteList:input_type -> hall.EmptyRequest
	11, // 9: hall.Hall.GetCurrency:input_type -> hall.GetCurrencyRequest
	14, // 10: hall.Hall.GetPopUpBulletin:input_type -> hall.GetPopUpBulletinRequest
	16, // 11: hall.Hall.UpdatePopUpBulletin:input_type -> hall.UpdatePopUpBulletinRequest
	17, // 12: hall.Hall.DeletePopUpBulletin:input_type -> hall.DeletePopUpBulletinRequest
	18, // 13: hall.Hall.SetHallConfig:input_type -> hall.SetHallConfigRequest
	19, // 14: hall.Hall.SetCurrency:input_type -> hall.SetCurrencyRequest
	20, // 15: hall.Hall.SetPresetCurrency:input_type -> hall.SetPresetCurrencyRequest
	4,  // 16: hall.Hall.GetHallIdByWebsite:output_type -> hall.GetHallIdByWebsiteResponse
	6,  // 17: hall.Hall.GetHallById:output_type -> hall.GetHallByIdResponse
	8,  // 18: hall.Hall.GetHallList:output_type -> hall.HallListResponse
	9,  // 19: hall.Hall.GetHallSiteList:output_type -> hall.GetHallSiteListResponse
	12, // 20: hall.Hall.GetCurrency:output_type -> hall.GetCurrencyResponse
	15, // 21: hall.Hall.GetPopUpBulletin:output_type -> hall.GetPopUpBulletinResponse
	2,  // 22: hall.Hall.UpdatePopUpBulletin:output_type -> hall.EmptyResponse
	2,  // 23: hall.Hall.DeletePopUpBulletin:output_type -> hall.EmptyResponse
	2,  // 24: hall.Hall.SetHallConfig:output_type -> hall.EmptyResponse
	2,  // 25: hall.Hall.SetCurrency:output_type -> hall.EmptyResponse
	2,  // 26: hall.Hall.SetPresetCurrency:output_type -> hall.EmptyResponse
	16, // [16:27] is the sub-list for method output_type
	5,  // [5:16] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_hall_proto_init() }
func file_hall_proto_init() {
	if File_hall_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_hall_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*BoolValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*EmptyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*EmptyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GetHallIdByWebsiteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*GetHallIdByWebsiteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*GetHallByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*GetHallByIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*HallListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*HallListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GetHallSiteListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*SiteList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*GetCurrencyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*GetCurrencyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*CurrencyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*GetPopUpBulletinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*GetPopUpBulletinResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*UpdatePopUpBulletinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*DeletePopUpBulletinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*SetHallConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*SetCurrencyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*SetPresetCurrencyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hall_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hall_proto_goTypes,
		DependencyIndexes: file_hall_proto_depIdxs,
		MessageInfos:      file_hall_proto_msgTypes,
	}.Build()
	File_hall_proto = out.File
	file_hall_proto_rawDesc = nil
	file_hall_proto_goTypes = nil
	file_hall_proto_depIdxs = nil
}
