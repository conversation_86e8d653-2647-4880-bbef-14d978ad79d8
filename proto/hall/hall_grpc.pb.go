// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: hall.proto

package hall

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Hall_GetHallIdByWebsite_FullMethodName  = "/hall.Hall/GetHallIdByWebsite"
	Hall_GetHallById_FullMethodName         = "/hall.Hall/GetHallById"
	Hall_GetHallList_FullMethodName         = "/hall.Hall/GetHallList"
	Hall_GetHallSiteList_FullMethodName     = "/hall.Hall/GetHallSiteList"
	Hall_GetCurrency_FullMethodName         = "/hall.Hall/GetCurrency"
	Hall_GetPopUpBulletin_FullMethodName    = "/hall.Hall/GetPopUpBulletin"
	Hall_UpdatePopUpBulletin_FullMethodName = "/hall.Hall/UpdatePopUpBulletin"
	Hall_DeletePopUpBulletin_FullMethodName = "/hall.Hall/DeletePopUpBulletin"
	Hall_SetHallConfig_FullMethodName       = "/hall.Hall/SetHallConfig"
	Hall_SetCurrency_FullMethodName         = "/hall.Hall/SetCurrency"
	Hall_SetPresetCurrency_FullMethodName   = "/hall.Hall/SetPresetCurrency"
)

// HallClient is the client API for Hall service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HallClient interface {
	GetHallIdByWebsite(ctx context.Context, in *GetHallIdByWebsiteRequest, opts ...grpc.CallOption) (*GetHallIdByWebsiteResponse, error)
	GetHallById(ctx context.Context, in *GetHallByIdRequest, opts ...grpc.CallOption) (*GetHallByIdResponse, error)
	GetHallList(ctx context.Context, in *HallListRequest, opts ...grpc.CallOption) (*HallListResponse, error)
	GetHallSiteList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetHallSiteListResponse, error)
	GetCurrency(ctx context.Context, in *GetCurrencyRequest, opts ...grpc.CallOption) (*GetCurrencyResponse, error)
	GetPopUpBulletin(ctx context.Context, in *GetPopUpBulletinRequest, opts ...grpc.CallOption) (*GetPopUpBulletinResponse, error)
	UpdatePopUpBulletin(ctx context.Context, in *UpdatePopUpBulletinRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	DeletePopUpBulletin(ctx context.Context, in *DeletePopUpBulletinRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	SetHallConfig(ctx context.Context, in *SetHallConfigRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	SetCurrency(ctx context.Context, in *SetCurrencyRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	SetPresetCurrency(ctx context.Context, in *SetPresetCurrencyRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
}

type hallClient struct {
	cc grpc.ClientConnInterface
}

func NewHallClient(cc grpc.ClientConnInterface) HallClient {
	return &hallClient{cc}
}

func (c *hallClient) GetHallIdByWebsite(ctx context.Context, in *GetHallIdByWebsiteRequest, opts ...grpc.CallOption) (*GetHallIdByWebsiteResponse, error) {
	out := new(GetHallIdByWebsiteResponse)
	err := c.cc.Invoke(ctx, Hall_GetHallIdByWebsite_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallClient) GetHallById(ctx context.Context, in *GetHallByIdRequest, opts ...grpc.CallOption) (*GetHallByIdResponse, error) {
	out := new(GetHallByIdResponse)
	err := c.cc.Invoke(ctx, Hall_GetHallById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallClient) GetHallList(ctx context.Context, in *HallListRequest, opts ...grpc.CallOption) (*HallListResponse, error) {
	out := new(HallListResponse)
	err := c.cc.Invoke(ctx, Hall_GetHallList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallClient) GetHallSiteList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetHallSiteListResponse, error) {
	out := new(GetHallSiteListResponse)
	err := c.cc.Invoke(ctx, Hall_GetHallSiteList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallClient) GetCurrency(ctx context.Context, in *GetCurrencyRequest, opts ...grpc.CallOption) (*GetCurrencyResponse, error) {
	out := new(GetCurrencyResponse)
	err := c.cc.Invoke(ctx, Hall_GetCurrency_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallClient) GetPopUpBulletin(ctx context.Context, in *GetPopUpBulletinRequest, opts ...grpc.CallOption) (*GetPopUpBulletinResponse, error) {
	out := new(GetPopUpBulletinResponse)
	err := c.cc.Invoke(ctx, Hall_GetPopUpBulletin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallClient) UpdatePopUpBulletin(ctx context.Context, in *UpdatePopUpBulletinRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Hall_UpdatePopUpBulletin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallClient) DeletePopUpBulletin(ctx context.Context, in *DeletePopUpBulletinRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Hall_DeletePopUpBulletin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallClient) SetHallConfig(ctx context.Context, in *SetHallConfigRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Hall_SetHallConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallClient) SetCurrency(ctx context.Context, in *SetCurrencyRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Hall_SetCurrency_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallClient) SetPresetCurrency(ctx context.Context, in *SetPresetCurrencyRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Hall_SetPresetCurrency_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HallServer is the server API for Hall service.
// All implementations must embed UnimplementedHallServer
// for forward compatibility
type HallServer interface {
	GetHallIdByWebsite(context.Context, *GetHallIdByWebsiteRequest) (*GetHallIdByWebsiteResponse, error)
	GetHallById(context.Context, *GetHallByIdRequest) (*GetHallByIdResponse, error)
	GetHallList(context.Context, *HallListRequest) (*HallListResponse, error)
	GetHallSiteList(context.Context, *EmptyRequest) (*GetHallSiteListResponse, error)
	GetCurrency(context.Context, *GetCurrencyRequest) (*GetCurrencyResponse, error)
	GetPopUpBulletin(context.Context, *GetPopUpBulletinRequest) (*GetPopUpBulletinResponse, error)
	UpdatePopUpBulletin(context.Context, *UpdatePopUpBulletinRequest) (*EmptyResponse, error)
	DeletePopUpBulletin(context.Context, *DeletePopUpBulletinRequest) (*EmptyResponse, error)
	SetHallConfig(context.Context, *SetHallConfigRequest) (*EmptyResponse, error)
	SetCurrency(context.Context, *SetCurrencyRequest) (*EmptyResponse, error)
	SetPresetCurrency(context.Context, *SetPresetCurrencyRequest) (*EmptyResponse, error)
	mustEmbedUnimplementedHallServer()
}

// UnimplementedHallServer must be embedded to have forward compatible implementations.
type UnimplementedHallServer struct {
}

func (UnimplementedHallServer) GetHallIdByWebsite(context.Context, *GetHallIdByWebsiteRequest) (*GetHallIdByWebsiteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHallIdByWebsite not implemented")
}
func (UnimplementedHallServer) GetHallById(context.Context, *GetHallByIdRequest) (*GetHallByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHallById not implemented")
}
func (UnimplementedHallServer) GetHallList(context.Context, *HallListRequest) (*HallListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHallList not implemented")
}
func (UnimplementedHallServer) GetHallSiteList(context.Context, *EmptyRequest) (*GetHallSiteListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHallSiteList not implemented")
}
func (UnimplementedHallServer) GetCurrency(context.Context, *GetCurrencyRequest) (*GetCurrencyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCurrency not implemented")
}
func (UnimplementedHallServer) GetPopUpBulletin(context.Context, *GetPopUpBulletinRequest) (*GetPopUpBulletinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPopUpBulletin not implemented")
}
func (UnimplementedHallServer) UpdatePopUpBulletin(context.Context, *UpdatePopUpBulletinRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePopUpBulletin not implemented")
}
func (UnimplementedHallServer) DeletePopUpBulletin(context.Context, *DeletePopUpBulletinRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePopUpBulletin not implemented")
}
func (UnimplementedHallServer) SetHallConfig(context.Context, *SetHallConfigRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetHallConfig not implemented")
}
func (UnimplementedHallServer) SetCurrency(context.Context, *SetCurrencyRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCurrency not implemented")
}
func (UnimplementedHallServer) SetPresetCurrency(context.Context, *SetPresetCurrencyRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPresetCurrency not implemented")
}
func (UnimplementedHallServer) mustEmbedUnimplementedHallServer() {}

// UnsafeHallServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HallServer will
// result in compilation errors.
type UnsafeHallServer interface {
	mustEmbedUnimplementedHallServer()
}

func RegisterHallServer(s grpc.ServiceRegistrar, srv HallServer) {
	s.RegisterService(&Hall_ServiceDesc, srv)
}

func _Hall_GetHallIdByWebsite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHallIdByWebsiteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServer).GetHallIdByWebsite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Hall_GetHallIdByWebsite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServer).GetHallIdByWebsite(ctx, req.(*GetHallIdByWebsiteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Hall_GetHallById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHallByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServer).GetHallById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Hall_GetHallById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServer).GetHallById(ctx, req.(*GetHallByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Hall_GetHallList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HallListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServer).GetHallList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Hall_GetHallList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServer).GetHallList(ctx, req.(*HallListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Hall_GetHallSiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServer).GetHallSiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Hall_GetHallSiteList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServer).GetHallSiteList(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Hall_GetCurrency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurrencyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServer).GetCurrency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Hall_GetCurrency_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServer).GetCurrency(ctx, req.(*GetCurrencyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Hall_GetPopUpBulletin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPopUpBulletinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServer).GetPopUpBulletin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Hall_GetPopUpBulletin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServer).GetPopUpBulletin(ctx, req.(*GetPopUpBulletinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Hall_UpdatePopUpBulletin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePopUpBulletinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServer).UpdatePopUpBulletin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Hall_UpdatePopUpBulletin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServer).UpdatePopUpBulletin(ctx, req.(*UpdatePopUpBulletinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Hall_DeletePopUpBulletin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePopUpBulletinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServer).DeletePopUpBulletin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Hall_DeletePopUpBulletin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServer).DeletePopUpBulletin(ctx, req.(*DeletePopUpBulletinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Hall_SetHallConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetHallConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServer).SetHallConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Hall_SetHallConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServer).SetHallConfig(ctx, req.(*SetHallConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Hall_SetCurrency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCurrencyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServer).SetCurrency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Hall_SetCurrency_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServer).SetCurrency(ctx, req.(*SetCurrencyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Hall_SetPresetCurrency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPresetCurrencyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServer).SetPresetCurrency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Hall_SetPresetCurrency_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServer).SetPresetCurrency(ctx, req.(*SetPresetCurrencyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Hall_ServiceDesc is the grpc.ServiceDesc for Hall service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Hall_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hall.Hall",
	HandlerType: (*HallServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetHallIdByWebsite",
			Handler:    _Hall_GetHallIdByWebsite_Handler,
		},
		{
			MethodName: "GetHallById",
			Handler:    _Hall_GetHallById_Handler,
		},
		{
			MethodName: "GetHallList",
			Handler:    _Hall_GetHallList_Handler,
		},
		{
			MethodName: "GetHallSiteList",
			Handler:    _Hall_GetHallSiteList_Handler,
		},
		{
			MethodName: "GetCurrency",
			Handler:    _Hall_GetCurrency_Handler,
		},
		{
			MethodName: "GetPopUpBulletin",
			Handler:    _Hall_GetPopUpBulletin_Handler,
		},
		{
			MethodName: "UpdatePopUpBulletin",
			Handler:    _Hall_UpdatePopUpBulletin_Handler,
		},
		{
			MethodName: "DeletePopUpBulletin",
			Handler:    _Hall_DeletePopUpBulletin_Handler,
		},
		{
			MethodName: "SetHallConfig",
			Handler:    _Hall_SetHallConfig_Handler,
		},
		{
			MethodName: "SetCurrency",
			Handler:    _Hall_SetCurrency_Handler,
		},
		{
			MethodName: "SetPresetCurrency",
			Handler:    _Hall_SetPresetCurrency_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "hall.proto",
}
