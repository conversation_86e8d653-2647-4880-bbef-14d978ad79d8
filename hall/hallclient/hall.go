// Code generated by goctl. DO NOT EDIT.
// Source: hall.proto

package hallclient

import (
	"context"

	"gbh/proto/hall"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	BoolValue                  = hall.BoolValue
	CurrencyInfo               = hall.CurrencyInfo
	DeletePopUpBulletinRequest = hall.DeletePopUpBulletinRequest
	EmptyRequest               = hall.EmptyRequest
	EmptyResponse              = hall.EmptyResponse
	GetCurrencyRequest         = hall.GetCurrencyRequest
	GetCurrencyResponse        = hall.GetCurrencyResponse
	GetHallByIdRequest         = hall.GetHallByIdRequest
	GetHallByIdResponse        = hall.GetHallByIdResponse
	GetHallIdByWebsiteRequest  = hall.GetHallIdByWebsiteRequest
	GetHallIdByWebsiteResponse = hall.GetHallIdByWebsiteResponse
	GetHallSiteListResponse    = hall.GetHallSiteListResponse
	GetPopUpBulletinRequest    = hall.GetPopUpBulletinRequest
	GetPopUpBulletinResponse   = hall.GetPopUpBulletinResponse
	HallListRequest            = hall.HallListRequest
	HallListResponse           = hall.HallListResponse
	SetCurrencyRequest         = hall.SetCurrencyRequest
	SetHallConfigRequest       = hall.SetHallConfigRequest
	SetPresetCurrencyRequest   = hall.SetPresetCurrencyRequest
	SiteList                   = hall.SiteList
	UpdatePopUpBulletinRequest = hall.UpdatePopUpBulletinRequest

	Hall interface {
		GetHallIdByWebsite(ctx context.Context, in *GetHallIdByWebsiteRequest, opts ...grpc.CallOption) (*GetHallIdByWebsiteResponse, error)
		GetHallById(ctx context.Context, in *GetHallByIdRequest, opts ...grpc.CallOption) (*GetHallByIdResponse, error)
		GetHallList(ctx context.Context, in *HallListRequest, opts ...grpc.CallOption) (*HallListResponse, error)
		GetHallSiteList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetHallSiteListResponse, error)
		GetCurrency(ctx context.Context, in *GetCurrencyRequest, opts ...grpc.CallOption) (*GetCurrencyResponse, error)
		GetPopUpBulletin(ctx context.Context, in *GetPopUpBulletinRequest, opts ...grpc.CallOption) (*GetPopUpBulletinResponse, error)
		UpdatePopUpBulletin(ctx context.Context, in *UpdatePopUpBulletinRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		DeletePopUpBulletin(ctx context.Context, in *DeletePopUpBulletinRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SetHallConfig(ctx context.Context, in *SetHallConfigRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SetCurrency(ctx context.Context, in *SetCurrencyRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SetPresetCurrency(ctx context.Context, in *SetPresetCurrencyRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	}

	defaultHall struct {
		cli zrpc.Client
	}
)

func NewHall(cli zrpc.Client) Hall {
	return &defaultHall{
		cli: cli,
	}
}

func (m *defaultHall) GetHallIdByWebsite(ctx context.Context, in *GetHallIdByWebsiteRequest, opts ...grpc.CallOption) (*GetHallIdByWebsiteResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetHallIdByWebsite(ctx, in, opts...)
}

func (m *defaultHall) GetHallById(ctx context.Context, in *GetHallByIdRequest, opts ...grpc.CallOption) (*GetHallByIdResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetHallById(ctx, in, opts...)
}

func (m *defaultHall) GetHallList(ctx context.Context, in *HallListRequest, opts ...grpc.CallOption) (*HallListResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetHallList(ctx, in, opts...)
}

func (m *defaultHall) GetHallSiteList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetHallSiteListResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetHallSiteList(ctx, in, opts...)
}

func (m *defaultHall) GetCurrency(ctx context.Context, in *GetCurrencyRequest, opts ...grpc.CallOption) (*GetCurrencyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetCurrency(ctx, in, opts...)
}

func (m *defaultHall) GetPopUpBulletin(ctx context.Context, in *GetPopUpBulletinRequest, opts ...grpc.CallOption) (*GetPopUpBulletinResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetPopUpBulletin(ctx, in, opts...)
}

func (m *defaultHall) UpdatePopUpBulletin(ctx context.Context, in *UpdatePopUpBulletinRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.UpdatePopUpBulletin(ctx, in, opts...)
}

func (m *defaultHall) DeletePopUpBulletin(ctx context.Context, in *DeletePopUpBulletinRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.DeletePopUpBulletin(ctx, in, opts...)
}

func (m *defaultHall) SetHallConfig(ctx context.Context, in *SetHallConfigRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.SetHallConfig(ctx, in, opts...)
}

func (m *defaultHall) SetCurrency(ctx context.Context, in *SetCurrencyRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.SetCurrency(ctx, in, opts...)
}

func (m *defaultHall) SetPresetCurrency(ctx context.Context, in *SetPresetCurrencyRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.SetPresetCurrency(ctx, in, opts...)
}
