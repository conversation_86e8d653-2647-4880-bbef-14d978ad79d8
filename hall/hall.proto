syntax = "proto3";

package hall;

option go_package = "proto/hall";

message BoolValue { bool value = 1; }
message EmptyRequest {}
message EmptyResponse {}

message GetHallIdByWebsiteRequest { string website = 1; }

message GetHallIdByWebsiteResponse { uint32 hall_id = 1; }

message GetHallByIdRequest { uint32 hall_id = 1; }

message GetHallByIdResponse {
  uint32 hall_id = 1;
  string name = 2;
  string login_code = 3;
  bool enable = 4;
}

message HallListRequest { BoolValue enable = 1; }

message HallListResponse { repeated GetHallByIdResponse data = 1; }

message GetHallSiteListResponse { repeated SiteList site_list = 1; }

message SiteList {
  string big_group = 1;
  string site_group = 2;
  string site_name = 3;
  string login_code = 4;
  uint32 hall_id = 5;
}

message GetCurrencyRequest { uint32 hall_id = 1; }

message GetCurrencyResponse { repeated CurrencyInfo currency_info = 1; }

message CurrencyInfo {
  bool preset = 1;
  string currency = 2;
}

message GetPopUpBulletinRequest { uint32 hall_id = 1; }

message GetPopUpBulletinResponse {
  uint32 hall_id = 1;
  bool enable = 2;
  uint32 role = 3;
  string zh_tw = 4;
  string zh_cn = 5;
  string en = 6;
  string th = 7;
  string ja = 8;
  string ko = 9;
  string vi = 10;
  string created_at = 11;
}

message UpdatePopUpBulletinRequest {
  uint32 hall_id = 1;
  BoolValue enable = 2;
}

message DeletePopUpBulletinRequest { uint32 hall_id = 1; }

message SetHallConfigRequest {
  uint32 hall_id = 1;
  string login_code = 2;
  string name = 3;
}

message SetCurrencyRequest {
  uint32 hall_id = 1;
  repeated string currency_list = 2;
}

message SetPresetCurrencyRequest {
  uint32 hall_id = 1;
  string currency = 2;
}

service Hall {
  rpc GetHallIdByWebsite(GetHallIdByWebsiteRequest)
      returns (GetHallIdByWebsiteResponse);
  rpc GetHallById(GetHallByIdRequest) returns (GetHallByIdResponse);
  rpc GetHallList(HallListRequest) returns (HallListResponse);
  rpc GetHallSiteList(EmptyRequest) returns (GetHallSiteListResponse);
  rpc GetCurrency(GetCurrencyRequest) returns (GetCurrencyResponse);
  rpc GetPopUpBulletin(GetPopUpBulletinRequest)
      returns (GetPopUpBulletinResponse);
  rpc UpdatePopUpBulletin(UpdatePopUpBulletinRequest) returns (EmptyResponse);
  rpc DeletePopUpBulletin(DeletePopUpBulletinRequest) returns (EmptyResponse);
  rpc SetHallConfig(SetHallConfigRequest) returns (EmptyResponse);
  rpc SetCurrency(SetCurrencyRequest) returns (EmptyResponse);
  rpc SetPresetCurrency(SetPresetCurrencyRequest) returns (EmptyResponse);
}
