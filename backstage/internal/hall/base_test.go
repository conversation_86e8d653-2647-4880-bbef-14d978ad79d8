package hall

import (
	"context"
	"gbh/hall/hallclient"

	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
)

var (
	ctx context.Context
)

type mockHallRPC struct{ mock.Mock }

func (s *mockHallRPC) GetHallIdByWebsite(ctx context.Context, in *hallclient.GetHallIdByWebsiteRequest, _ ...grpc.CallOption) (*hallclient.GetHallIdByWebsiteResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}
	return resp.(*hallclient.GetHallIdByWebsiteResponse), nil
}

func (s *mockHallRPC) GetHallById(ctx context.Context, in *hallclient.GetHallByIdRequest, _ ...grpc.CallOption) (*hallclient.GetHallByIdResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}
	return resp.(*hallclient.GetHallByIdResponse), nil
}

func (s *mockHallRPC) GetHallList(ctx context.Context, in *hallclient.HallListRequest, _ ...grpc.CallOption) (*hallclient.HallListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}
	return resp.(*hallclient.HallListResponse), nil
}

func (s *mockHallRPC) GetHallSiteList(ctx context.Context, in *hallclient.EmptyRequest, _ ...grpc.CallOption) (*hallclient.GetHallSiteListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*hallclient.GetHallSiteListResponse), nil
}

func (s *mockHallRPC) GetCurrency(ctx context.Context, in *hallclient.GetCurrencyRequest, _ ...grpc.CallOption) (*hallclient.GetCurrencyResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*hallclient.GetCurrencyResponse), nil
}

func (s *mockHallRPC) GetPopUpBulletin(ctx context.Context, in *hallclient.GetPopUpBulletinRequest, _ ...grpc.CallOption) (*hallclient.GetPopUpBulletinResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*hallclient.GetPopUpBulletinResponse), nil
}

func (s *mockHallRPC) UpdatePopUpBulletin(ctx context.Context, in *hallclient.UpdatePopUpBulletinRequest, _ ...grpc.CallOption) (*hallclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*hallclient.EmptyResponse), nil
}

func (s *mockHallRPC) DeletePopUpBulletin(ctx context.Context, in *hallclient.DeletePopUpBulletinRequest, _ ...grpc.CallOption) (*hallclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*hallclient.EmptyResponse), nil
}

func (s *mockHallRPC) SetHallConfig(ctx context.Context, in *hallclient.SetHallConfigRequest, _ ...grpc.CallOption) (*hallclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*hallclient.EmptyResponse), nil
}

func (s *mockHallRPC) SetCurrency(ctx context.Context, in *hallclient.SetCurrencyRequest, _ ...grpc.CallOption) (*hallclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func (s *mockHallRPC) SetPresetCurrency(ctx context.Context, in *hallclient.SetPresetCurrencyRequest, _ ...grpc.CallOption) (*hallclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func newMockHallRPC() *mockHallRPC {
	return &mockHallRPC{}
}

func init() {
	ctx = context.Background()
}
