package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/logger"
	"gbh/proto/sportgame"
	"gbh/rpcserver"
	"gbh/sportgame/internal/config"

	"gbh/sportgame/internal/server"
	"gbh/sportgame/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/sportgame.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)
	if logErr != nil {
		log.Fatal(logErr)
	}

	db, err := database.New(c.WagersDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	ctx := svc.NewServiceContext(c)
	ctx.WagersDB = db

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		sportgame.RegisterSportGameServer(grpcServer, server.NewSportGameServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()
	s.AddUnaryInterceptors(rpcserver.LoggerInterceptor)

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
