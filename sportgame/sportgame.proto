syntax = "proto3";

package sportgame;
option go_package = "proto/sportgame";

message GameListRequest { string lang = 1; }

message GameListResponse { repeated GameInfo game_info = 1; }

message GameInfo {
  uint32 id = 1;
  string name = 2;
}

message Uint32Value { uint32 value = 1; }

message StringValue { string value = 1; }

message GameLinkRequest {
  string lang = 1;
  Uint32Value device = 2;
  StringValue ots = 3;
  StringValue enter_page = 4;
  Uint32Value exit_option = 5;
  StringValue exit_url_param = 6;
}

message GameLinkResponse { string url = 1; }

message SportWagersByBetTimeRequest {
  uint32 hall_id = 1;
  string start_time = 2;
  string end_time = 3;
  uint32 agent_id = 4;
  uint32 page = 5;
  uint32 page_limit = 6;
  Uint32Value game_id = 7;
}

message SportWagersByModifiedTimeRequest {
  uint32 hall_id = 1;
  string start_time = 2;
  string end_time = 3;
  uint32 page = 4;
  uint32 page_limit = 5;
  Uint32Value agent_id = 6;
  Uint32Value game_id = 7;
}

message Wagers {
  uint64 wagers_id = 1;
  string round_date = 2;
  string modified_date = 3;
  string match_date = 4;
  uint32 game_id = 5;
  string currency = 6;
  double exchange_rate = 7;
  int32 result_status = 8;
  double bet_amount = 9;
  StringValue account_date = 10;
  uint32 platform = 11;
  double payoff = 12;
  double commissionable = 13;
  uint32 user_id = 14;
}

message Pagination {
  uint32 current_page = 1;
  uint32 page_limit = 2;
  uint32 total = 3;
  uint32 total_page = 4;
}

message WagersResponse {
  repeated Wagers wagers = 1;
  Pagination pagination = 2;
}

message SubWagersURLRequest {
  uint32 user_id = 1;
  uint64 wagers_id = 2;
  string lang = 3;
}

message SubWagersURLResponse { string url = 1; }

message CheckWagersByIDRequest {
  uint64 id = 1;
  uint32 user_id = 2;
}

message GetCategoryRequest { string lang = 1; }

message Category {
  uint32 id = 1;
  string name = 2;
}

message GetCategoryResponse { repeated Category category = 1; }

message GameplayBetLimitRequest {
  uint32 user_id = 1;
  uint32 category_id = 2;
  string lang = 3;
}

message GroupNamePair {
  string key = 1;
  string value = 2;
}

message BetLimitSetting {
  double bet_limit = 1;
  double game_limit = 2;
}

message BetLimitSettingPair {
  string key = 1;
  BetLimitSetting value = 2;
}

message GameplayBetLimitResponse {
  repeated GroupNamePair group_name = 1;
  repeated BetLimitSettingPair setting = 2;
}

message UnfinishStatisRequest {
  string lang = 1;
  uint32 user_id = 2;
  Uint32Value game_id = 3;
}

message UnfinishStatisResponse {
  repeated GameWagersStatis game_list = 1;
  uint32 total_count = 3;
  double total_amount = 4;
  repeated WagersInfo wagers = 5;
}

message GameWagersStatis {
  uint32 id = 1;
  string name = 2;
  uint32 count = 3;
  double bet_amount = 4;
  double payoff = 5;
  double commissionable = 6;
}

message WagersInfo {
  uint64 wagers_id = 1;
  string sport_name = 2;
  string add_date = 3;
  string odd_type = 4;
  double bet_amount = 5;
  string bet_state = 6;
  double payoff = 7;
  double commissionable = 8;
}

message EmptyResponse {}

message WagersDetailRequest {
  uint64 wagers_id = 1;
  string lang = 2;
}

message WagersDetail {
  string match_date = 1;
  string sport_name = 2;
  string competition_name = 3;
  string region_name = 4;
  string match_name = 5;
  string market_name = 6;
  string selection_name = 7;
  double price = 8;
  string match_info = 9;
  string state = 10;
  string state_name = 11;
  string selection_score = 12;
  string resettlement_reason = 13;
}

message WagersDetailResponse { repeated WagersDetail data = 1; }

message FinishStatisRequest {
  uint32 user_id = 1;
  string start_date = 2;
  string end_date = 3;
  string lang = 4;
}

message FinishStatisResponse {
  repeated DateWagersStatis wagers = 1;
  WagersStatis total = 2;
}

message DateWagersStatis {
  string date = 1;
  string weekday = 2;
  repeated GameWagersStatis game_list = 3;
  WagersStatis subtotal = 4;
}

message WagersStatis {
  double bet_amount = 1;
  double payoff = 2;
  double commissionable = 3;
}

message FinishWagersRequest {
  uint32 user_id = 1;
  string start_date = 2;
  string end_date = 3;
  string lang = 4;
  uint32 game_id = 5;
}

message FinishWagersResponse {
  repeated GameInfo game_list = 1;
  repeated WagersInfo wagers = 2;
  WagersStatis total = 3;
}

message GetWagersRequest {
  string start_round_date = 1;
  string end_round_date = 2;
  uint32 hall_id = 3;
  repeated uint32 user_id = 4;
  uint64 wagers_id = 5;
  repeated string game_id = 6;
  Uint32Value result = 7;
  double min_bet_amount = 8;
  double max_bet_amount = 9;
  double min_payoff = 10;
  double max_payoff = 11;
  string close_date = 12;
  uint32 page = 13;
  uint32 page_limit = 14;
  string order = 15;
}

message GetWagersResponse {
  repeated FetchWagersByDB wagers = 1;
  Pagination pagination = 2;
  Total sub_total = 3;
  Total total = 4;
}

message FetchWagersByDB {
  uint64 wagers_id = 1;
  string wagers_time = 2;
  uint32 hierarchy = 3;
  uint32 portal = 4;
  int32 wagers_type = 5;
  uint32 platform = 6;
  uint32 client = 7;
  uint32 game_id = 8;
  uint32 user_id = 9;
  string round_date = 10;
  string round_time = 11;
  double bet_amount = 12;
  double commissionable = 13;
  string currency = 14;
  double exchange_rate = 15;
  int32 result = 16;
  double payoff = 17;
  uint32 hall_id = 18;
  string round_serial = 19;
  string reference_id = 20;
  string event_time = 21;
  string settled_time = 22;
  string odd_type = 23;
  string market = 24;
  string rule = 25;
  string modified_date = 26;
}

message Total {
  uint32 number = 1;
  double bet_amount = 2;
  double commissionable = 3;
  double payoff = 4;
}

service SportGame {
  rpc GameList(GameListRequest) returns (GameListResponse);
  rpc GameLink(GameLinkRequest) returns (GameLinkResponse);
  rpc WagersByBetTime(SportWagersByBetTimeRequest) returns (WagersResponse);
  rpc WagersByModifiedTime(SportWagersByModifiedTimeRequest)
      returns (WagersResponse);
  rpc SubWagersURL(SubWagersURLRequest) returns (SubWagersURLResponse);
  rpc CheckWagersByID(CheckWagersByIDRequest) returns (EmptyResponse);
  rpc GetCategory(GetCategoryRequest) returns (GetCategoryResponse);
  rpc WagersDetail(WagersDetailRequest) returns (WagersDetailResponse);
  rpc GameplayBetLimit(GameplayBetLimitRequest)
      returns (GameplayBetLimitResponse);
  rpc UnfinishStatis(UnfinishStatisRequest) returns (UnfinishStatisResponse);
  rpc FinishStatis(FinishStatisRequest) returns (FinishStatisResponse);
  rpc FinishWagers(FinishWagersRequest) returns (FinishWagersResponse);
  rpc GetWagers(GetWagersRequest) returns (GetWagersResponse);
}